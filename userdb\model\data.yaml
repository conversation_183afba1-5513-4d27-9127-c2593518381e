# YOLO Dataset Configuration for Solar Panel Defect Detection
# This file defines the classes and structure for your model

# Number of classes
nc: 5

# Class names
names:
  0: 'Bird-drop'
  1: 'Dusty'
  2: 'Electrical-Damage'
  3: 'Physical-Damage'
  4: 'Clean'

# Optional: Dataset paths (not needed for inference)
# train: path/to/train/images
# val: path/to/val/images
# test: path/to/test/images

# Model configuration
# This YAML file helps YOLO understand your model's class structure
