<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header("Location: ../PHP/Dronlytics Login Page.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Dronlytics Images Upload Section</title>
  <link rel="stylesheet" href="../CSS/Dronlytics Images Upload.css"> 
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
</head>
<body>

<!-- === HEADER === -->
<?php include '../PHP/Dronlytics Header After Login.php'; ?>

<!-- === MAIN CONTENT === -->
<div class="main-content">
  <div class="upload-container">
    <h2 class="upload-title">Upload Your Images</h2>
    <p class="upload-subtitle">Select your drone images for solar panel defect analysis</p>

    <div class="form-group">
      <label for="projectName">Project Name</label>
      <input type="text" id="projectName" placeholder="Enter Your Project Name" />
    </div>

    <form id="uploadForm" enctype="multipart/form-data">
      <label class="btn-upload" for="fileInput">📤 Browse Files</label>
      <input type="file" id="fileInput" multiple accept=".jpg,.jpeg,.png,.zip" />
      <button type="button" id="serverUploadBtn" style="display:none;">Upload to Server</button>
    </form>

    <div class="upload-info">
      <strong>Supported formats:</strong> JPG, PNG or ZIP files<br>
      <strong>Note:</strong> You can upload a single image, multiple images, or an entire ZIP file containing images
    </div>

    <div id="uploadProgress" style="display:none;">
      <div class="progress-bar">
        <div class="progress-fill" id="progressFill"></div>
      </div>
      <p id="progressText">Processing...</p>
    </div>
  </div>
</div>

<!-- === FOOTER === -->
<?php include '../PHP/Dronlytics Footer V2.php'; ?>


<!-- === JAVASCRIPT === -->
<script>
  // Check if JSZip loaded successfully
  window.addEventListener('load', function() {
    if (typeof JSZip === 'undefined') {
      console.error('❌ JSZip library failed to load');
      // Try loading from alternative CDN
      const script = document.createElement('script');
      script.src = 'https://unpkg.com/jszip@3.10.1/dist/jszip.min.js';
      script.onload = () => console.log('✅ JSZip loaded from fallback CDN');
      script.onerror = () => console.error('❌ JSZip fallback also failed');
      document.head.appendChild(script);
    } else {
      console.log('✅ JSZip library loaded successfully');
    }
  });

  // NAVIGATION BAR
  // DROPDOWN TOGGLE
  function toggleDropdown(id) {
    document.querySelectorAll('.dropdown').forEach(el => {
      if (el.id !== id) el.classList.remove('show');
    });
    document.getElementById(id).classList.toggle("show");
  }

  // CLOSE DROPDOWN WHEN CLICKING OUTSIDE
  window.addEventListener("click", function (e) {
    document.querySelectorAll(".dropdown").forEach(dropdown => {
      if (!dropdown.contains(e.target)) {
        dropdown.classList.remove("show");
      }
    });
  });

  // REDIRECTED BASED ON USER SESSION
  function redirectTo(targetPage) {
    const userId = sessionStorage.getItem("loggedInUserID");
    if (!userId) {
      alert("User session expired. Please login again.");
      window.location.href = "../PHP/Dronlytics Login Page.php";
      return;
    }
    window.location.href = `${targetPage}?user=${encodeURIComponent(userId)}`;
  }

  // CLEAR PREVIOUS IMAGE SESSION DATA
  sessionStorage.removeItem("singleMultipleImages");
  sessionStorage.removeItem("extractedZipImages");
  sessionStorage.removeItem("projectName");



  // FILE INPUT HANDLER
  document.getElementById("fileInput").addEventListener("change", async (event) => {
    const files = event.target.files;
    const projectName = document.getElementById("projectName").value.trim();

    if (!projectName) {
      alert("Please enter a project name.");
      return;
    }

    sessionStorage.setItem("projectName", projectName);

    try {
      // ZIP FILE UPLOAD - Automatic processing
      if (files.length === 1 && files[0].name.toLowerCase().endsWith(".zip")) {
        console.log("🗜️ ZIP file detected, extracting...");

        // Show progress
        document.getElementById("uploadProgress").style.display = "block";
        document.getElementById("progressText").textContent = "Extracting ZIP file...";

        const zipFile = files[0];

        // Check if JSZip is loaded
        if (typeof JSZip === 'undefined') {
          throw new Error("JSZip library not loaded. Please refresh the page and try again.");
        }

        const zip = await JSZip.loadAsync(zipFile);
        const extractedBlobs = [];

        console.log(`📁 ZIP contains ${Object.keys(zip.files).length} files`);

        for (const filename of Object.keys(zip.files)) {
          // Skip directories
          if (zip.files[filename].dir) {
            continue;
          }

          if (/\.(jpg|jpeg|png)$/i.test(filename)) {
            console.log(`📷 Extracting image: ${filename}`);
            const blob = await zip.files[filename].async("blob");
            const file = new File([blob], filename, { type: blob.type || "image/jpeg" });
            extractedBlobs.push(file);
          }
        }

        console.log(`✅ Extracted ${extractedBlobs.length} images from ZIP`);

        if (extractedBlobs.length === 0) {
          document.getElementById("uploadProgress").style.display = "none";
          alert("No image files found in the ZIP file. Please ensure your ZIP contains JPG, JPEG, or PNG images.");
          return;
        }

        document.getElementById("progressText").textContent = `Processing ${extractedBlobs.length} images...`;

        const filesToStore = await Promise.all(extractedBlobs.map(file => fileToBase64Object(file)));
        sessionStorage.setItem("extractedZipImages", JSON.stringify(filesToStore));

        document.getElementById("progressText").textContent = "Redirecting to image selection...";

        setTimeout(() => {
          window.location.href = "../PHP/Dronlytics Images Selection.php";
        }, 1000);

        return;
      }

      // MULTIPLE IMAGES UPLOAD
      const validImages = Array.from(files).filter(f => /\.(jpg|jpeg|png)$/i.test(f.name));
      if (validImages.length === 0) {
        alert("Please upload JPG/PNG images or a ZIP file.");
        return;
      }

      console.log(`📷 Processing ${validImages.length} individual images`);
      const filesToStore = await Promise.all(validImages.map(file => fileToBase64Object(file)));
      sessionStorage.setItem("finalizedImages", JSON.stringify(filesToStore));
      window.location.href = "../PHP/Dronlytics Image List.php";

    } catch (error) {
      console.error("❌ Upload error:", error);
      document.getElementById("uploadProgress").style.display = "none";
      alert("Error processing files: " + error.message);
    }
  });

  // SERVER-SIDE UPLOAD HANDLER
  async function handleServerSideUpload(files, projectName) {
    const userId = sessionStorage.getItem("loggedInUserID");
    if (!userId) {
      alert("User session expired. Please login again.");
      window.location.href = "login.html";
      return;
    }

    const formData = new FormData();
    formData.append("project_name", projectName);
    formData.append("user_id", userId);

    // Add all files to form data
    for (let i = 0; i < files.length; i++) {
      formData.append("files[]", files[i]);
    }

    // Show progress
    document.getElementById("uploadProgress").style.display = "block";
    document.getElementById("progressText").textContent = "Uploading files to server...";

    try {
      const response = await fetch("../PHP/process_zip_upload.php", {
        method: "POST",
        body: formData
      });

      const result = await response.json();

      if (result.success) {
        document.getElementById("progressText").textContent = `Successfully processed ${result.image_count} images!`;

        // Store the processed images for display
        sessionStorage.setItem("finalizedImages", JSON.stringify(result.images));

        setTimeout(() => {
          window.location.href = "../PHP/Dronlytics Image List.php";
        }, 2000);
      } else {
        throw new Error(result.message || "Upload failed");
      }
    } catch (error) {
      alert("Upload failed: " + error.message);
      document.getElementById("uploadProgress").style.display = "none";
    }
  }

  // CONVERT IMAGE FILE INTO BASE64 WITH STANDARDIZED SIZE
  async function fileToBase64Object(file) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const reader = new FileReader();

      reader.onload = () => {
        img.src = reader.result;
      };

      img.onload = () => {
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");

        // STANDARDIZED DIMENSIONS FOR CONSISTENT DISPLAY
        const maxWidth = 800;
        const maxHeight = 600;

        // Calculate aspect ratio and new dimensions
        let { width, height } = img;
        const aspectRatio = width / height;

        // Resize to fit within max dimensions while maintaining aspect ratio
        if (width > maxWidth || height > maxHeight) {
          if (aspectRatio > maxWidth / maxHeight) {
            // Width is the limiting factor
            width = maxWidth;
            height = maxWidth / aspectRatio;
          } else {
            // Height is the limiting factor
            height = maxHeight;
            width = maxHeight * aspectRatio;
          }
        }

        // Set canvas to standardized dimensions
        canvas.width = width;
        canvas.height = height;

        // Draw image with standardized size
        ctx.drawImage(img, 0, 0, width, height);

        const standardizedBase64 = canvas.toDataURL("image/jpeg", 0.85);

        resolve({
          name: file.name,
          type: "image/jpeg",
          size: standardizedBase64.length,
          data: standardizedBase64,
          width: width,
          height: height
        });
      };

      img.onerror = reject;
      reader.onerror = reject;

      reader.readAsDataURL(file);
    });
  }

     // RESPONSIVE MENU TOGGLE BUTTON
  function toggleMobileMenu() {
    const sidebar = document.getElementById("mobileSidebar");
    sidebar.classList.toggle("open");
  }

    // RESPONSIVE MENU TOGGLE BUTTON
    // CLICK OUTSIDE TO CLOSE
  document.addEventListener("click", function (e) {
    const sidebar = document.getElementById("mobileSidebar");
    const toggle = document.querySelector(".mobile-menu-toggle");

    if (!sidebar.contains(e.target) && !toggle.contains(e.target)) {
      sidebar.classList.remove("open");
    }
  });

  // RESPONSIVE MENU TOGGLE BUTTON 
  // OVERLAY
  function toggleMobileMenu() {
  const sidebar = document.getElementById("mobileSidebar");
  const overlay = document.getElementById("mobileOverlay");
  const isOpen = sidebar.classList.contains("open");

  if (isOpen) {
    sidebar.classList.remove("open");
    overlay.classList.remove("show");
  } else {
    sidebar.classList.add("open");
    overlay.classList.add("show");
  }
}

function closeMobileMenu() {
  document.getElementById("mobileSidebar").classList.remove("open");
  document.getElementById("mobileOverlay").classList.remove("show");
}
</script>

</body>
</html>
