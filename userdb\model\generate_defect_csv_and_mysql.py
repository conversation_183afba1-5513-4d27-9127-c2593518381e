
import os
from ultralytics import YOLO
import cv2
import numpy as np
import pandas as pd
from tqdm import tqdm
import pymysql
import csv
# Google Drive API imports
from googleapiclient.discovery import build
from googleapiclient.http import MediaIoBaseDownload
from google.oauth2 import service_account
import io



def download_all_images_from_gdrive(folder_id, local_dir, service_account_path):
    SCOPES = ['https://www.googleapis.com/auth/drive']
    creds = service_account.Credentials.from_service_account_file(service_account_path, scopes=SCOPES)
    service = build('drive', 'v3', credentials=creds)
    # List all image files in the folder
    query = f"'{folder_id}' in parents and (mimeType contains 'image/') and trashed = false"
    results = service.files().list(q=query, fields="files(id, name, createdTime)").execute()
    files = results.get('files', [])
    os.makedirs(local_dir, exist_ok=True)
    if not files:
        print(f"No images found in Google Drive folder {folder_id}.")
        return []
    downloaded_files = []
    for file in files:
        file_id = file['id']
        file_name = file['name']
        print(f"Downloading image: {file_name} from Google Drive folder {folder_id}...")
        request = service.files().get_media(fileId=file_id)
        fh = io.FileIO(os.path.join(local_dir, file_name), 'wb')
        downloader = MediaIoBaseDownload(fh, request)
        done = False
        while not done:
            status, done = downloader.next_chunk()
        fh.close()
        print(f"Downloaded: {file_name}")
        downloaded_files.append(file_name)
    return downloaded_files

def run_yolo_and_save_to_mysql():
    # === Step 1: Download all images from Google Drive ===
    folder_id = "1nqR8J7zqCCVUx50If76g05vE7rZCzT7c"  # Provided by user
    service_account_path = os.path.join(os.path.dirname(__file__), '..', 'service_account.json')
    image_dir = "temp_images"  # Temporary local folder for images
    output_dir = "yolo_summary_output"
    os.makedirs(output_dir, exist_ok=True)
    all_images = download_all_images_from_gdrive(folder_id, image_dir, service_account_path)
    if not all_images:
        print("No images to process.")
        return

    # === Step 2: Load Model ===
    model_path = os.path.join(os.path.dirname(__file__), "best1.pt")
    model = YOLO(model_path)

    # === Step 3: Init result container ===
    results_list = []

    # === Step 4: Inference and extract info ===
    annotated_dir = os.path.join("..", "static", "annotated")
    os.makedirs(annotated_dir, exist_ok=True)
    conn_update = pymysql.connect(host="localhost", user="root", password="", db="userdb", charset="utf8mb4")
    cursor_update = conn_update.cursor()

    # Process each image
    for image_file in all_images:
        if not image_file.lower().endswith((".jpg", ".png", ".jpeg")):
            print(f"File {image_file} is not a supported image type. Skipping.")
            continue

        image_path = os.path.join(image_dir, image_file)
        results = model(image_path)[0]
        masks = results.masks
        names = results.names

        # Save annotated image
        annotated_filename = f"{os.path.splitext(image_file)[0]}_annotated.jpg"
        annotated_path = os.path.join(annotated_dir, annotated_filename)
        results.save(filename=annotated_path)

        # Update Annotated_Image_Path in image_uploaded table
        annotated_db_path = f"static/annotated/{annotated_filename}"
        image_id = os.path.splitext(image_file)[0]
        print(f"Trying to update Annotated_Image_Path for Image_ID: {image_id} (file: {image_file})")
        try:
            cursor_update.execute(
                "UPDATE image_uploaded SET Annotated_Image_Path=%s WHERE Image_ID=%s",
                (annotated_db_path, image_id)
            )
            conn_update.commit()
            print(f"✅ Updated Annotated_Image_Path for {image_id} to {annotated_db_path}")
        except Exception as e:
            print(f"❌ Error updating Annotated_Image_Path for {image_file}: {e}")

        if masks is not None and len(masks.data) > 0:
            for i, mask in enumerate(masks.data):
                if results.boxes is None or len(results.boxes.cls) <= i:
                    continue

                label_id = int(results.boxes.cls[i].item())
                confidence = float(results.boxes.conf[i].item())
                label_name = names[label_id]
                mask_np = mask.cpu().numpy()

                if mask_np.sum() == 0:
                    continue

                pixel_area = int(mask_np.sum())
                label_lower = label_name.lower()

                if "clean" in label_lower:
                    continue
                elif "physical" in label_lower:
                    maintenance = "Replace"
                elif "bird" in label_lower or "dust" in label_lower:
                    maintenance = "Clean"
                elif "electrical" in label_lower:
                    maintenance = "Repair"
                else:
                    maintenance = "Unknown"

                results_list.append({
                    "image": image_file,
                    "defect type": label_name,
                    "defect count": 1,
                    "confidence level": confidence,
                    "pixel area": pixel_area,
                    "maintenance type": maintenance
                })

    # === Step 5: Save only defect_summary.csv ===
    if results_list:
        df = pd.DataFrame(results_list)
        df_summary = df.groupby(["image", "defect type", "maintenance type"]) \
                       .agg({
                           "defect count": "sum",
                           "confidence level": "mean",
                           "pixel area": "sum"
                       }).reset_index()
        summary_csv_path = os.path.join(output_dir, "defect_summary.csv")
        df_summary.to_csv(summary_csv_path, index=False)
        print(f"✅ Saved: {summary_csv_path}")

        # === Step 6: Insert CSV rows into MySQL ===
        conn = pymysql.connect(host="localhost", user="root", password="", db="userdb", charset="utf8mb4")
        cursor = conn.cursor()
        with open(summary_csv_path, newline="") as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                image_id = os.path.splitext(row["image"])[0]
                print(f"Trying to insert defect for Image_ID: {image_id} (file: {row['image']})")
                try:
                    cursor.execute("""
                        INSERT INTO image_defects (Image_ID, Defect_Type, Defect_Count, Confidence_Level, Pixel_Area, Maintenance_Type)
                        VALUES (%s, %s, %s, %s, %s, %s)
                    """, (
                        image_id,
                        row["defect type"],
                        row["defect count"],
                        row["confidence level"],
                        row["pixel area"],
                        row["maintenance type"]
                    ))
                    print(f"✅ Inserted defect for {image_id}")
                except Exception as e:
                    print(f"❌ MySQL insert error for {row['image']}: {e}")
        conn.commit()
        cursor.close()
        conn.close()
        print("✅ All defect data inserted into MySQL.")
    else:
        print("⚠️ No defects detected — CSV not generated.")

    cursor_update.close()
    conn_update.close()

    # Optional: Clean up temp_images folder after processing
    import shutil
    shutil.rmtree(image_dir)

if __name__ == "__main__":
    run_yolo_and_save_to_mysql()
