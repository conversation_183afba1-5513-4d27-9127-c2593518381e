<?php
header('Content-Type: application/json');
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Enable CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Check if files were uploaded
    if (!isset($_FILES['files']) || !isset($_POST['project_name']) || !isset($_POST['user_id'])) {
        throw new Exception("Missing required data");
    }

    $projectName = trim($_POST['project_name']);
    $userId = trim($_POST['user_id']);
    $uploadedFiles = $_FILES['files'];
    
    if (empty($projectName) || empty($userId)) {
        throw new Exception("Project name and user ID are required");
    }

    // Create temporary directory for processing
    $tempDir = sys_get_temp_dir() . '/dronlytics_upload_' . uniqid();
    if (!mkdir($tempDir, 0755, true)) {
        throw new Exception("Failed to create temporary directory");
    }

    $processedImages = [];
    $totalImageCount = 0;

    // Process each uploaded file
    for ($i = 0; $i < count($uploadedFiles['name']); $i++) {
        $fileName = $uploadedFiles['name'][$i];
        $fileTmpName = $uploadedFiles['tmp_name'][$i];
        $fileError = $uploadedFiles['error'][$i];
        
        if ($fileError !== UPLOAD_ERR_OK) {
            error_log("Upload error for file $fileName: $fileError");
            continue;
        }

        $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        
        // Handle ZIP files
        if ($fileExtension === 'zip') {
            $images = extractImagesFromZip($fileTmpName, $tempDir);
            $processedImages = array_merge($processedImages, $images);
            $totalImageCount += count($images);
        }
        // Handle individual image files
        elseif (in_array($fileExtension, ['jpg', 'jpeg', 'png'])) {
            $image = processIndividualImage($fileTmpName, $fileName);
            if ($image) {
                $processedImages[] = $image;
                $totalImageCount++;
            }
        }
    }

    // Clean up temporary directory
    removeDirectory($tempDir);

    if ($totalImageCount === 0) {
        throw new Exception("No valid images found in uploaded files");
    }

    echo json_encode([
        'success' => true,
        'message' => "Successfully processed $totalImageCount images",
        'image_count' => $totalImageCount,
        'images' => $processedImages,
        'project_name' => $projectName,
        'user_id' => $userId
    ]);

} catch (Exception $e) {
    error_log("ZIP upload error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Extract images from ZIP file
 */
function extractImagesFromZip($zipPath, $tempDir) {
    $images = [];
    
    $zip = new ZipArchive();
    $result = $zip->open($zipPath);
    
    if ($result !== TRUE) {
        throw new Exception("Failed to open ZIP file: Error code $result");
    }

    for ($i = 0; $i < $zip->numFiles; $i++) {
        $fileName = $zip->getNameIndex($i);
        $fileInfo = pathinfo($fileName);
        
        // Skip directories and non-image files
        if (empty($fileInfo['extension']) || 
            !in_array(strtolower($fileInfo['extension']), ['jpg', 'jpeg', 'png'])) {
            continue;
        }

        // Extract file to temporary location
        $extractPath = $tempDir . '/' . basename($fileName);
        $fileContent = $zip->getFromIndex($i);
        
        if ($fileContent === false) {
            error_log("Failed to extract file: $fileName");
            continue;
        }

        file_put_contents($extractPath, $fileContent);
        
        // Process the extracted image
        $image = processIndividualImage($extractPath, $fileName);
        if ($image) {
            $images[] = $image;
        }
        
        // Clean up extracted file
        unlink($extractPath);
    }
    
    $zip->close();
    return $images;
}

/**
 * Process individual image file
 */
function processIndividualImage($imagePath, $originalName) {
    try {
        // Get image info
        $imageInfo = getimagesize($imagePath);
        if (!$imageInfo) {
            error_log("Invalid image file: $originalName");
            return null;
        }

        // Create image resource based on type
        switch ($imageInfo[2]) {
            case IMAGETYPE_JPEG:
                $sourceImage = imagecreatefromjpeg($imagePath);
                break;
            case IMAGETYPE_PNG:
                $sourceImage = imagecreatefrompng($imagePath);
                break;
            default:
                error_log("Unsupported image type: $originalName");
                return null;
        }

        if (!$sourceImage) {
            error_log("Failed to create image resource: $originalName");
            return null;
        }

        // Standardize image size (same logic as frontend)
        $maxWidth = 800;
        $maxHeight = 600;
        $originalWidth = $imageInfo[0];
        $originalHeight = $imageInfo[1];
        
        $aspectRatio = $originalWidth / $originalHeight;
        
        // Calculate new dimensions
        if ($originalWidth > $maxWidth || $originalHeight > $maxHeight) {
            if ($aspectRatio > $maxWidth / $maxHeight) {
                $newWidth = $maxWidth;
                $newHeight = $maxWidth / $aspectRatio;
            } else {
                $newHeight = $maxHeight;
                $newWidth = $maxHeight * $aspectRatio;
            }
        } else {
            $newWidth = $originalWidth;
            $newHeight = $originalHeight;
        }

        // Create resized image
        $resizedImage = imagecreatetruecolor($newWidth, $newHeight);
        
        // Handle PNG transparency
        if ($imageInfo[2] === IMAGETYPE_PNG) {
            imagealphablending($resizedImage, false);
            imagesavealpha($resizedImage, true);
            $transparent = imagecolorallocatealpha($resizedImage, 255, 255, 255, 127);
            imagefill($resizedImage, 0, 0, $transparent);
        }
        
        imagecopyresampled($resizedImage, $sourceImage, 0, 0, 0, 0, 
                          $newWidth, $newHeight, $originalWidth, $originalHeight);

        // Convert to base64
        ob_start();
        imagejpeg($resizedImage, null, 85);
        $imageData = ob_get_contents();
        ob_end_clean();
        
        $base64 = 'data:image/jpeg;base64,' . base64_encode($imageData);

        // Clean up
        imagedestroy($sourceImage);
        imagedestroy($resizedImage);

        return [
            'name' => $originalName,
            'type' => 'image/jpeg',
            'size' => strlen($base64),
            'data' => $base64,
            'width' => $newWidth,
            'height' => $newHeight
        ];

    } catch (Exception $e) {
        error_log("Error processing image $originalName: " . $e->getMessage());
        return null;
    }
}

/**
 * Remove directory and all contents
 */
function removeDirectory($dir) {
    if (!is_dir($dir)) {
        return;
    }
    
    $files = array_diff(scandir($dir), ['.', '..']);
    foreach ($files as $file) {
        $path = $dir . '/' . $file;
        is_dir($path) ? removeDirectory($path) : unlink($path);
    }
    rmdir($dir);
}
?>
