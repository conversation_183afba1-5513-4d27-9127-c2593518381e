/* === GOOGLE FONT === */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* === GLOBAL RESET === */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  height: 100%;
  margin: 0;
  padding: 0;
}

/* === BODY STYLES === */
body {
  font-family: 'Inter', sans-serif;
  color: #333;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
  height: 100vh;
}

/* === LOGIN SECTION STYLES === */
.login-section {
  background: linear-gradient(135deg, rgba(30, 58, 95, 0.75), rgba(74, 144, 226, 0.65)),
              url('https://images.unsplash.com/photo-1473968512647-3e447244af8f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
  background-size: cover;
  background-position: center;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 2rem;
  position: relative;
  transform: translateY(30px);
}

/* === LOGIN BOX STYLES === */
.login-box {
  background: white;
  padding: 2rem 1.5rem;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 400px;
  text-align: center;
}

.login-box h2 {
  font-size: 1.5rem;
  color: #1e3a5f;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.login-box form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* === INPUT STYLES === */
.login-box input[type="email"],
.login-box input[type="password"] {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.95rem;
  background: white;
  color: #333;
}

.login-box input[type="submit"] {
  background: linear-gradient(135deg, #4299e1, #2d5a87);
  color: white;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.login-box input[type="submit"]:hover {
  background: linear-gradient(135deg, #63b3ed, #4299e1);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(66, 153, 225, 0.3);
}

/* === FORM LINKS STYLES === */
.form-links {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1rem;
}

.form-links a {
  color: #4299e1;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.85rem;
  transition: 0.3s;
}

.form-links a:hover {
  color: #2d5a87;
  text-decoration: underline;
}

/* === FOOTER STYLES === */
footer {
  background: #1e3a5f;
  color: white;
  text-align: center;
  padding: 1rem 0;
  position: relative;
  bottom: 0;
  left: 0;
  right: 0;
}

footer p {
  margin: 0;
  opacity: 0.8;
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
  :root {
    --header-h-mobile: 64px;      /* height of fixed header */
    --gap-below-header: 1.4cm;    /* desired space below header */
  }

  /* Container flex to center vertically */
  .login-section {
    min-height: calc(100vh - var(--header-h-mobile)); /* full height minus header */
    display: flex;
    flex-direction: column;
    justify-content: center;  /* center vertically */
    align-items: center;
    padding: 0 1rem;
    box-sizing: border-box;
  }

  /* Login box styling */
  .login-box {
    padding: 1.75rem 1.25rem;
    margin: calc(var(--gap-below-header) / 2) 0; /* equal gap above & below */
  }

  /* Optional: tighten header spacing on mobile */
  .header-container {
    flex-direction: column;
    gap: 1rem;
  }
}
