
/* === GOOGLE FONT === */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* === GLOBAL RESETS & BODY STYLING === */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  color: #333;
  background: rgba(74, 144, 226, 0.03);
  overflow-x: hidden;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

html {
  scroll-behavior: smooth;
}

/* === HEADER SECTION === */
header {
  background: #0a2b47;
  padding: 0.75rem 0;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100vw;
  z-index: 1000;
  overflow: hidden;
  box-sizing: border-box;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
  width: 100%;
  box-sizing: border-box;
}

/* === LOGO === */
.logo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-image {
  width: 40px;
  height: 40px;
  object-fit: contain;
  border-radius: 8px;
  padding: 4px;
}

.logo-text {
  font-size: 1.6rem;
  font-weight: 700;
  color: white;
}

/* === NAVIGATION BAR === */
nav {
  display: flex;
  justify-content: flex-end;
  gap: 1.5rem;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 1rem;
}

nav a {
  text-decoration: none;
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

nav a:hover {
  color: #4a90e2;
  background: rgba(255, 255, 255, 0.1);
}

/* === RESPONSIVE MENU TOGGLE BUTTON === */
.mobile-menu-toggle {
  display: none;
  font-size: 1.7rem;
  color: white;
  cursor: pointer;
  z-index: 1100;
  margin-right: 1.5rem;
  padding: 0.6rem 0.5rem;
  transition: transform 0.3s ease, background 0.3s ease, color 0.3s ease;
  border-radius: 6px;
}

.mobile-menu-toggle:hover {
  transform: scale(1.1);
}

/* === MOBILE SIDEBAR MENU === */
.mobile-sidebar {
  position: fixed;
  top: 0;
  right: -260px;
  width: 260px;
  height: 100%;
  background: #0a2b47;
  box-shadow: -2px 0 10px rgba(0,0,0,0.2);
  display: flex;
  flex-direction: column;
  padding: 4rem 1.5rem;
  gap: 1.5rem;
  transition: right 0.3s ease;
  z-index: 1050;
}

.mobile-sidebar a {
  color: white;
  text-decoration: none;
  font-size: 1rem;
  font-weight: 500;
  padding: 0.75rem 1rem;
  border-radius: 6px;
}

.mobile-sidebar a:hover {
  background: rgba(255,255,255,0.1);
}

/* === ACTIVE SIDEBAR === */
.mobile-sidebar.open {
  right: 0;
}

/* === OVERLAY BEHIND SIDEBAR === */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1040;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease;
}

/* === Show overlay when menu is open === */
.mobile-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* === RESPONSIVE BEHAVIOR === */
/* === RESPONSIVE BEHAVIOR === */
@media (max-width: 1024px) {
  /* hide desktop nav */
  nav { display: none !important; }

  /* show hamburger (fixed right) */
  .mobile-menu-toggle {
    display: block !important;
    margin: 0;                  /* override any previous margins */
  }

  /* keep logo + DRONLYTICS pinned left */
  .header-container {
    display: flex;
    align-items: center;
    justify-content: flex-start; /* brand left */
    gap: 1rem;
    padding-right: 64px;         /* space for the fixed hamburger */
  }
  .logo {
    margin-right: auto;          /* push hamburger to the far right */
  }
}

/* == Normalize header height & brand alignment on mobile (≤1024px) == */
@media (max-width: 1024px) {
  /* lock a consistent header size */
  header {
    height: 64px;
    padding: 0 !important;
  }

  .header-container {
    height: 64px;
    padding: 0 12px !important;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;
    padding-right: 64px !important;
  }

  /* BRAND */
  .logo,
  .logo a {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    gap: 8px;
    margin: 0 !important;
    padding: 0 !important;
    text-align: left !important;
  }
  .logo { margin-right: auto !important; }

  .logo-image {
    height: 30px;
    width: auto !important;
    padding: 0 !important;
    display: block;
  }

  .logo-text {
    font-size: 21px;
    line-height: 30px;
    margin: 0 !important;
    color: #fff;
    display: inline-flex;
    align-items: center;
  }

  /* HAMBURGER */
  .mobile-menu-toggle {
    display: block !important;
    position: fixed;
    right: 12px;
    top: calc(64px / 2);
    transform: translateY(-50%);
    left: auto;
    margin: 0 !important;
    z-index: 1100;
  }

  nav { display: none !important; }
  .mobile-dropdown-content a { color: #fff; }
  .mobile-dropdown-content a:hover { color: #dbeafe; }

  /* === ADD 1.4 cm space below header === */
  .main-content,
  .content-area,
  .page-content {
    margin-top: calc(64px + 1.4cm) !important;
  }
}

/* == Mobile hard-pin: brand left & perfectly centered (≤1024px) == */
@media (max-width: 1024px) {
  header {
    height: 64px;
    padding: 0 !important;
  }

  .header-container {
    position: relative;
    height: 64px;
    padding: 0 16px !important;
  }

  .logo,
  .logo a {
    position: absolute !important;
    top: 50%;
    left: 12px;
    transform: translateY(-50%);
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    gap: 8px;
    margin: 0 !important;
    padding: 0 !important;
    text-align: left !important;
    width: auto !important;
  }
  .logo-image { height: 30px; width: auto !important; display: block; }
  .logo-text  { font-size: 21px; line-height: 1; margin: 0 !important; color: #fff; }

  .mobile-menu-toggle {
    display: block !important;
    position: fixed;
    right: 12px;
    top: 32px; /* 64px / 2 */
    transform: translateY(-50%);
    left: auto;
    margin: 0 !important;
    z-index: 1100;
  }

  nav { display: none !important; }
  .mobile-dropdown-content a { color: #fff; }
  .mobile-dropdown-content a:hover { color: #dbeafe; }
}
