/* === GOOGLE FONT === */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* === GLOBAL RESET & BASE FONT === */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  color: #333;
  background: rgba(74, 144, 226, 0.03);
  overflow-x: hidden;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

html {
  scroll-behavior: smooth;
}

/* === HEADER CONTAINER === */
header {
  background: #0a2b47;
  padding: 0.75rem 0;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
  max-width: 100%;
}

/* === LOGO SECTION === */
.logo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-image {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.logo-text {
  font-size: 1.6rem;
  font-weight: 800;
  letter-spacing: 0.5px;
  color: white;
}

/* === NAVIGATION === */
nav {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 2rem;
}

nav a {
  text-decoration: none;
  color: white;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

nav a:hover {
  color: #4a90e2;
  background: rgba(255, 255, 255, 0.1);
}

/* === LOGIN BUTTON === */
.login-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: white !important;
  text-decoration: none;
  background: transparent;
  padding: 0;
  border-radius: 0;
  transition: all 0.3s ease;
}

.login-button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #4a90e2 !important;
}

.login-icon {
  width: 21px;
  height: 21px;
  fill: currentColor;
}

/* === LOGOUT LINK === */
.logout-link {
  display: flex;
  align-items: center;
  gap: 0.4rem;
}

.logout-icon {
  width: 18px;
  height: 18px;
  fill: white;
}

/* === DROPDOWN MENU === */
.dropdown {
  position: relative;
}

.dropdown-content {
  display: none;
  position: absolute;
  top: 100%;
  background: #2a4a6f;
  border-radius: 6px;
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
  min-width: 160px;
  z-index: 999;
}

.dropdown-content a {
  color: white;
  padding: 10px 16px;
  display: block;
  font-size: 0.9rem;
  text-decoration: none;
}

.dropdown-content a:hover {
  background-color: #4a90e2;
}

.dropdown.show .dropdown-content {
  display: block;
}

/* === RESPONSIVE MENU TOGGLE BUTTON === */
.mobile-menu-toggle {
  display: none;
  font-size: 1.7rem;
  color: white;
  cursor: pointer;
  z-index: 1100;
  margin-right: 1.5rem;
  padding: 0.6rem 0.5rem;
  transition: transform 0.3s ease, background 0.3s ease, color 0.3s ease;
  border-radius: 6px;
}

.mobile-menu-toggle:hover {
  transform: scale(1.1);
}

/* === MOBILE SIDEBAR MENU === */
.mobile-sidebar {
  position: fixed;
  top: 0;
  right: -260px;
  width: 260px;
  height: 100%;
  background: #0a2b47;
  box-shadow: -2px 0 10px rgba(0,0,0,0.2);
  display: flex;
  flex-direction: column;
  padding: 4rem 1.5rem;
  gap: 1.5rem;
  transition: right 0.3s ease;
  z-index: 1050;
}

.mobile-sidebar a {
  color: white;
  text-decoration: none;
  font-size: 1rem;
  font-weight: 500;
  padding: 0.75rem 1rem;
  border-radius: 6px;
}

.mobile-sidebar a:hover {
  background: rgba(255,255,255,0.1);
}

/* === ACTIVE SIDEBAR === */
.mobile-sidebar.open {
  right: 0;
}

/* === OVERLAY BEHIND SIDEBAR === */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1040;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease;
}

/* === Show overlay when menu is open === */
.mobile-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* === RESPONSIVE BEHAVIOR === */
:root {
  --header-h-mobile: 64px; /* header height in mobile view */
}

/* === MOBILE BEHAVIOR (≤1024px) === */
@media (max-width: 1024px) {
  /* Allow scrolling */
  html, body {
    height: auto !important;
    min-height: 100vh;
    overflow-x: hidden;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch;
  }

  /* Hide desktop nav */
  nav {
    display: none !important;
  }

  /* Header styling */
  header {
    height: var(--header-h-mobile);
    padding: 0 !important;
  }

  /* Header container & brand alignment */
  .header-container {
    position: relative;
    height: var(--header-h-mobile);
    padding: 0 16px !important;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;
    padding-right: 64px; /* space for fixed hamburger */
  }

  .logo,
  .logo a {
    position: absolute !important;
    top: 50%;
    left: 12px;
    transform: translateY(-50%);
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    gap: 8px;
    margin: 0 !important;
    padding: 0 !important;
    text-align: left !important;
    width: auto !important;
  }

  .logo-image {
    height: 30px;
    width: auto !important;
    display: block;
  }

  .logo-text {
    font-size: 21px;
    line-height: 1;
    margin: 0 !important;
    color: #fff;
  }

  /* Hamburger button */
  .mobile-menu-toggle {
    display: block !important;
    position: fixed;
    right: 12px;
    top: calc(var(--header-h-mobile) / 2);
    transform: translateY(-50%);
    left: auto;
    margin: 0 !important;
    z-index: 1100;
    font-size: 1.7rem;
    color: #fff;
    cursor: pointer;
    padding: 0.6rem 0.5rem;
    border-radius: 6px;
    transition: transform 0.3s ease, background 0.3s ease, color 0.3s ease;
  }
  .mobile-menu-toggle:hover {
    transform: scale(1.1);
  }

  /* Sidebar */
  .mobile-sidebar {
    position: fixed;
    top: 0;
    right: -260px;
    width: 260px;
    height: 100%;
    background: #002f5f;
    box-shadow: -2px 0 10px rgba(0,0,0,0.2);
    display: flex;
    flex-direction: column;
    padding: 4rem 1.5rem;
    gap: 1.5rem;
    transition: right 0.3s ease;
    z-index: 1050;
  }
  .mobile-sidebar.open { right: 0; }

  .mobile-sidebar a {
    color: #fff;
    text-decoration: none;
    font-size: 1rem;
    font-weight: 500;
    padding: 0.75rem 1rem;
    border-radius: 6px;
  }
  .mobile-sidebar a:hover {
    background: rgba(255,255,255,0.1);
  }

  /* Overlay */
  .mobile-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0,0,0,0.4);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
  }
  .mobile-overlay.show {
    opacity: 1;
    visibility: visible;
  }

  /* Mobile dropdowns */
  .mobile-dropdown { position: relative; margin-bottom: 8px; }
  .mobile-dropdown > span {
    display: block;
    font-weight: 600;
    padding: 8px 16px;
    color: #4a90e2;
    cursor: pointer;
  }
  .mobile-dropdown-content { display: block; padding-left: 24px; }
  .mobile-dropdown-content a {
    display: block;
    color: #fff;
    padding: 4px 0;
    text-decoration: none;
  }
  .mobile-dropdown-content a:hover { color: #dbeafe; }
}
