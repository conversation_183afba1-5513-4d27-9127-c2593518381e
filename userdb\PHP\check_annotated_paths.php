<?php
include("db.php");

// Check annotated image paths in database
$query = "SELECT Image_ID, Annotated_Image_Path, Upload_Timestamp FROM image_uploaded WHERE Annotated_Image_Path IS NOT NULL ORDER BY Upload_Timestamp DESC LIMIT 10";
$result = $conn->query($query);

echo "<h2>🔍 Annotated Image Paths in Database</h2>";

if ($result->num_rows > 0) {
    echo "<table border='1' style='width:100%; border-collapse: collapse;'>";
    echo "<tr><th>Image ID</th><th>Annotated Path</th><th>Upload Time</th><th>File Check</th></tr>";
    
    while($row = $result->fetch_assoc()) {
        $path = $row["Annotated_Image_Path"];
        $imageId = $row["Image_ID"];
        
        // Check if file exists
        $fileExists = "❌ Not found";
        $testPaths = [
            "../" . $path,
            "../PHP/" . $path,
            "../static/annotated/" . $imageId . "_annotated.jpg",
            "../python/" . $path
        ];
        
        foreach ($testPaths as $testPath) {
            if (file_exists($testPath)) {
                $fileExists = "✅ Found at: " . $testPath;
                break;
            }
        }
        
        echo "<tr>";
        echo "<td>" . $imageId . "</td>";
        echo "<td style='word-break: break-all;'>" . htmlspecialchars($path) . "</td>";
        echo "<td>" . $row["Upload_Timestamp"] . "</td>";
        echo "<td>" . $fileExists . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>❌ No annotated images found in database.</p>";
}

// Also check what files actually exist in the static/annotated directory
echo "<h3>📁 Files in static/annotated directory:</h3>";
$annotatedDir = "../static/annotated/";
if (is_dir($annotatedDir)) {
    $files = scandir($annotatedDir);
    $imageFiles = array_filter($files, function($file) {
        return preg_match('/\.(jpg|jpeg|png)$/i', $file);
    });
    
    if (!empty($imageFiles)) {
        echo "<ul>";
        foreach ($imageFiles as $file) {
            echo "<li>" . $file . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>❌ No image files found in static/annotated directory.</p>";
    }
} else {
    echo "<p>❌ static/annotated directory does not exist.</p>";
}

// Check python/static/annotated directory too
echo "<h3>📁 Files in python/static/annotated directory:</h3>";
$pythonAnnotatedDir = "../python/static/annotated/";
if (is_dir($pythonAnnotatedDir)) {
    $files = scandir($pythonAnnotatedDir);
    $imageFiles = array_filter($files, function($file) {
        return preg_match('/\.(jpg|jpeg|png)$/i', $file);
    });
    
    if (!empty($imageFiles)) {
        echo "<ul>";
        foreach ($imageFiles as $file) {
            echo "<li>" . $file . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>❌ No image files found in python/static/annotated directory.</p>";
    }
} else {
    echo "<p>❌ python/static/annotated directory does not exist.</p>";
}

$conn->close();
?>
