#!/usr/bin/env python3
"""
Test script to add a crack defect and verify maintenance type
"""

import mysql.connector

DB_CONFIG = {
    'host': 'localhost', 'user': 'root', 'password': '',
    'database': 'userdb', 'charset': 'utf8mb4', 'autocommit': True
}

def get_maintenance_type(defect_type, bbox_area=None):
    """Get maintenance type based on defect type"""
    defect_lower = defect_type.lower().replace('_', '-').replace(' ', '-')
    
    if 'clean' in defect_lower:
        return 'none'
    elif 'bird-drop' in defect_lower or 'dusty' in defect_lower:
        return 'clean'
    elif 'electrical-damage' in defect_lower:
        return 'repair'
    elif 'crack' in defect_lower:
        return 'replace'  # Crack defects always require replacement
    elif 'physical-damage' in defect_lower:
        return 'replace'  # Physical damage always requires replacement
    else:
        return 'inspect'

def main():
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Add a crack defect to test
        test_defects = [
            ('IMG001', 'Crack', 3, 'replace'),
            ('IMG002', 'Physical-Damage', 2, 'replace'),  # Updated: Physical-damage now requires replace
            ('IMG003', 'Electrical-damage', 1, 'repair')
        ]
        
        print("🧪 Testing maintenance type mapping...")
        
        for image_id, defect_type, count, expected_maintenance in test_defects:
            # Test the function
            calculated_maintenance = get_maintenance_type(defect_type)
            
            print(f"\n📋 Defect: {defect_type}")
            print(f"   Expected: {expected_maintenance}")
            print(f"   Calculated: {calculated_maintenance}")
            print(f"   Match: {'✅' if calculated_maintenance.lower() == expected_maintenance.lower() else '❌'}")
            
            # Add to database
            cursor.execute("""
                INSERT INTO image_defects (Image_ID, Defect_Type, Defect_Count, Maintenance_Type)
                VALUES (%s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE 
                Defect_Count = VALUES(Defect_Count),
                Maintenance_Type = VALUES(Maintenance_Type)
            """, (image_id, defect_type, count, calculated_maintenance))
        
        conn.commit()
        
        # Verify database contents
        print("\n📊 Current defects in database:")
        cursor.execute("SELECT Image_ID, Defect_Type, Defect_Count, Maintenance_Type FROM image_defects ORDER BY Image_ID, Defect_Type")
        results = cursor.fetchall()
        
        for row in results:
            print(f"   {row[0]}: {row[1]} x{row[2]} → {row[3]}")
        
        conn.close()
        print("\n✅ Test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
