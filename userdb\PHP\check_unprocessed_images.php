<?php
include("db.php");

// Check for unprocessed images
$query = "SELECT Image_ID, Image_Path, User_ID, Upload_Timestamp FROM image_uploaded WHERE Annotated_Image_Path IS NULL ORDER BY Upload_Timestamp DESC LIMIT 10";
$result = $conn->query($query);

echo "<h2>🔍 Unprocessed Images</h2>";

if ($result->num_rows > 0) {
    echo "<table border='1'>";
    echo "<tr><th>Image ID</th><th>User ID</th><th>Image Path</th><th>Upload Time</th></tr>";
    
    while($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row["Image_ID"] . "</td>";
        echo "<td>" . $row["User_ID"] . "</td>";
        echo "<td>" . substr($row["Image_Path"], 0, 50) . "...</td>";
        echo "<td>" . $row["Upload_Timestamp"] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<br><a href='flask_auto_starter.php' target='_blank'>🚀 Trigger Processing</a>";
} else {
    echo "<p>✅ No unprocessed images found.</p>";
}

$conn->close();
?>
