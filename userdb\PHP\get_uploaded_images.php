<?php
header("Content-Type: application/json");

// Connect to database
$conn = new mysqli("localhost", "root", "", "userdb");

// Check connection
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(["error" => "Database connection failed"]);
    exit();
}

// Get user ID from URL
$user_id = $_GET['user'] ?? '';

if (empty($user_id)) {
    http_response_code(400);
    echo json_encode(["error" => "Missing user ID"]);
    exit();
}

// Query image records for this user
$query = "SELECT Image_ID, Image_Path FROM image_uploaded WHERE User_ID = ? ORDER BY Upload_Timestamp DESC";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $user_id);
$stmt->execute();
$result = $stmt->get_result();

// Prepare response
$images = [];
while ($row = $result->fetch_assoc()) {
    $images[] = $row;
}

// Return JSON response
echo json_encode($images);
?>
