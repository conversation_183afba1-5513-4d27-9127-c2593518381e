/* === GOOGLE FONT === */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* === GLOBAL RESETS & BODY STYLING === */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  color: #333;
  background: rgba(74, 144, 226, 0.03);
  overflow-x: hidden;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

html {
  scroll-behavior: smooth;
}


/* === MAIN CONTENT === */
.main-content {
  flex: 1;
  padding: 125px 32px 60px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  background: #f4faff;
}

/* === UPLOAD CONTAINER === */
.upload-container {
  background: white;
  padding: 2.5rem 2.5rem;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.08);
  text-align: center;
  max-width: 500px;
  width: 100%;
  border: 1px solid #f0f0f0;
  position: relative;
}

.upload-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4a90e2, #002f5f);
}

/* === UPLOAD HEADINGS === */
.upload-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1e3a5f;
  margin-bottom: 1.5rem;
  letter-spacing: 1px;
}

.upload-subtitle {
  font-size: 1rem;
  color: #666;
  margin-bottom: 2rem;
}

/* === FORM FIELD === */
.form-group {
  margin-bottom: 2rem;
}

.form-group label {
  font-weight: 600;
  color: #1e3a5f;
  font-size: 0.95rem;
  margin-bottom: 0.5rem;
  display: block;
}

input[type="text"] {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  background: #f8f9fa;
  text-align: center;
  transition: all 0.3s ease;
}

input[type="text"]:focus {
  outline: none;
  background: white;
  border-color: #4a90e2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

/* === BROWSE FILE BUTTON === */
.btn-upload {
  display: inline-block;
  background: linear-gradient(135deg, #4a90e2, #002f5f);
  color: white;
  font-weight: 600;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(74, 144, 226, 0.3);
}

.btn-upload:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(74, 144, 226, 0.4);
}

.btn-upload:active {
  transform: translateY(0);
}

input[type="file"] {
  display: none;
}

/* === SUPPORTED FORMAT NOTE === */
.upload-info {
  font-size: 0.9rem;
  color: #666;
  margin-top: 1rem;
  line-height: 1.5;
}

/* === PROGRESS BAR STYLES === */
#uploadProgress {
  margin-top: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #28a745;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #28a745, #20c997);
  border-radius: 4px;
  width: 0%;
  transition: width 0.3s ease;
  animation: progressPulse 2s infinite;
}

@keyframes progressPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

#progressText {
  font-size: 0.9rem;
  color: #28a745;
  font-weight: 500;
  margin: 0;
}

/* === RESPONSIVE (MOBILE) === */
@media (max-width: 480px) {
  .main-content {
    padding: 220px 20px 40px; /* Very large top padding to push content down */
    align-items: center;
    min-height: calc(100vh - 60px); /* Ensure footer stays at bottom */
  }

  .upload-container {
    padding: 2.2rem 1.5rem;
    margin-top: 30px; /* Additional margin for extra spacing */
    transform: translateY(20px); /* Optional: slight additional push */
  }
}

/* For tablets and larger mobile devices */
@media (min-width: 481px) and (max-width: 767px) {
  .main-content {
    padding: 250px 32px 60px; /* Even more space on larger devices */
  }
}

/* Adjust for small height devices */
@media (max-width: 480px) and (max-height: 700px) {
  .main-content {
    padding: 150px 20px 30px; /* Reduced but still substantial padding */
  }
}

/* === RESPONSIVE MENU TOGGLE BUTTON === */
.mobile-menu-toggle {
  display: none;
  font-size: 1.7rem;
  color: white;
  cursor: pointer;
  z-index: 1100;
  margin-right: 1.5rem;
  padding: 0.6rem 0.5rem;
  transition: transform 0.3s ease, background 0.3s ease, color 0.3s ease; 
  border-radius: 6px;
}

.mobile-menu-toggle:hover {
  transform: scale(1.1);
}



/* === MOBILE SIDEBAR MENU === */
.mobile-sidebar {
  position: fixed;
  top: 0;
  right: -260px;
  width: 260px;
  height: 100%;
  background: #002f5f;
  box-shadow: -2px 0 10px rgba(0,0,0,0.2);
  display: flex;
  flex-direction: column;
  padding: 4rem 1.5rem;
  gap: 1.5rem;
  transition: right 0.3s ease;
  z-index: 1050;
}

.mobile-sidebar a {
  color: white;
  text-decoration: none;
  font-size: 1rem;
  font-weight: 500;
  padding: 0.75rem 1rem;
  border-radius: 6px;
}

.mobile-sidebar a:hover {
  background: rgba(255,255,255,0.1);
}

/* === ACTIVE SIDEBAR === */
.mobile-sidebar.open {
  right: 0;
}

/* === OVERLAY BEHIND SIDEBAR === */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4); /* dark transparent */
  z-index: 1040;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease;
}

/* === Show overlay when menu is open === */
.mobile-overlay.show {
  opacity: 1;
  visibility: visible;
}


/* === RESPONSIVE BEHAVIOR === */
@media (max-width: 1024px) {
  nav {
    display: none;
  }

  .mobile-menu-toggle {
    display: block;
    margin-left: auto; /* push to the right end */
  }

  .header-container {
    gap: 1rem;
  }
}

@media (max-width: 1024px) {
  .main-content {
    padding: 60px 32px 60px;  /* More top space on desktops */
  }
}