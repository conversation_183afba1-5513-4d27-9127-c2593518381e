<?php
session_start();
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Dronlytics Sign Up</title>
  <link rel="stylesheet" href="../CSS/Dronlytics Sign Up Page.css">
</head>
<body>

<!-- === HEADER === -->
<?php include '..//PHP/Dronlytics Header Login Sign Up.php'; ?>

  <!-- === MAIN CONTENT === -->
  <section class="signup-section">
    <div class="signup-box">
      <h2>Create Account</h2>

      <?php
      // Display error messages
      if (isset($_SESSION['signup_errors'])) {
          echo '<div class="error-messages">';
          foreach ($_SESSION['signup_errors'] as $error) {
              echo '<p class="error-message">' . htmlspecialchars($error) . '</p>';
          }
          echo '</div>';
          unset($_SESSION['signup_errors']);
      }

      // Display success message
      if (isset($_SESSION['signup_success'])) {
          echo '<div class="success-message">' . htmlspecialchars($_SESSION['signup_success']) . '</div>';
          unset($_SESSION['signup_success']);
      }
      ?>

      <form action="../PHP/signup.php" method="post">
        <div class="form-group">
          <input type="text" name="fullname" placeholder="Full Name"
                 value="<?php echo isset($_SESSION['form_data']['fullname']) ? htmlspecialchars($_SESSION['form_data']['fullname']) : ''; ?>" required>
        </div>
        <div class="form-group">
          <input type="email" name="email" placeholder="Email"
                 value="<?php echo isset($_SESSION['form_data']['email']) ? htmlspecialchars($_SESSION['form_data']['email']) : ''; ?>" required>
        </div>
        <div class="form-group">
          <input type="password" name="password" placeholder="Password" required>
        </div>
        <div class="form-group">
          <input type="password" name="confirm-password" placeholder="Confirm Password" required>
        </div>
        <button type="submit" class="signup-button">Sign Up</button>
      </form>

      <a href="../PHP/Dronlytics Login Page.php" class="login-link">Already have an account? Log in</a>
    </div>
  </section>

  <!-- === FOOTER === -->
  <?php include '..//PHP/Dronlytics Footer V2.php'; ?>

<?php
// Clear form data after displaying
if (isset($_SESSION['form_data'])) {
    unset($_SESSION['form_data']);
}
?>

</body>
</html>
