<?php
$mysqli = new mysqli("localhost", "root", "", "userdb");
if ($mysqli->connect_error) {
    die(json_encode(["error" => "DB connection failed."]));
}

$user = $_GET['user'] ?? 'U001';

// Get panel summary
$stmt = $mysqli->prepare("
    SELECT 
        iu.Image_ID,
        iu.Project_Name,
        ir.Total_Panel_Count,
        ir.Healthy_Panel_Count,
        ir.Defect_Panel_Count,
        ir.Analysis_Date
    FROM image_uploaded iu
    LEFT JOIN image_results ir ON iu.Image_ID = ir.Image_ID
    WHERE iu.User_ID = ?
    ORDER BY ir.Analysis_Date DESC
");
$stmt->bind_param("s", $user);
$stmt->execute();
$result = $stmt->get_result();

$data = [];

while ($row = $result->fetch_assoc()) {
    $imageID = $row['Image_ID'];

    // Get defect breakdown
    $defectQuery = $mysqli->prepare("
        SELECT Defect_Type, Defect_Frequency
        FROM image_defects
        WHERE Image_ID = ?
    ");
    $defectQuery->bind_param("s", $imageID);
    $defectQuery->execute();
    $defectResult = $defectQuery->get_result();

    $defects = [];
    while ($d = $defectResult->fetch_assoc()) {
        $defects[$d['Defect_Type']] = (int)$d['Defect_Frequency'];
    }

    $data[] = [
        "Image_ID" => $imageID,
        "Project_Name" => $row["Project_Name"],
        "Total_Panel_Count" => (int)$row["Total_Panel_Count"],
        "Healthy_Panel_Count" => (int)$row["Healthy_Panel_Count"],
        "Defect_Panel_Count" => (int)$row["Defect_Panel_Count"],
        "Analysis_Date" => $row["Analysis_Date"],
        "Defects" => $defects
    ];
}

header('Content-Type: application/json');
echo json_encode($data);
?>
"""

# Save to file
path = "/mnt/data/get_dashboard_data.php"
with open(path, "w") as f:
    f.write(updated_php)

path
