<?php
include("db.php");

// Test image processing by adding a sample image to the database
$testImageId = "TEST001";
$testUserId = "U001"; // Make sure this user exists
$testImagePath = "https://drive.google.com/uc?export=view&id=1nqR8J7zqCCVUx50If76g05vE7rZCzT7c"; // Sample Google Drive image
$testProjectName = "Test Project";

echo "<h2>🧪 Test Image Processing</h2>";

// First, check if user exists
$userCheck = $conn->prepare("SELECT User_ID FROM user_data WHERE User_ID = ?");
$userCheck->bind_param("s", $testUserId);
$userCheck->execute();
$userResult = $userCheck->get_result();

if ($userResult->num_rows === 0) {
    echo "<p>❌ User $testUserId not found. Please create this user first or change the testUserId.</p>";
    exit;
}

// Delete existing test image if it exists
$deleteStmt = $conn->prepare("DELETE FROM image_uploaded WHERE Image_ID = ?");
$deleteStmt->bind_param("s", $testImageId);
$deleteStmt->execute();

// Insert test image
$insertStmt = $conn->prepare("INSERT INTO image_uploaded (Image_ID, Image_Path, User_ID, Project_Name) VALUES (?, ?, ?, ?)");
$insertStmt->bind_param("ssss", $testImageId, $testImagePath, $testUserId, $testProjectName);

if ($insertStmt->execute()) {
    echo "<p>✅ Test image added to database: $testImageId</p>";
    
    // Trigger processing
    echo "<p>🚀 Triggering image processing...</p>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "http://127.0.0.1:5000/process_new_images");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        echo "<p>✅ Processing triggered successfully!</p>";
        echo "<p>Response: " . htmlspecialchars($response) . "</p>";
        
        // Wait a moment and check if annotated path was updated
        sleep(2);
        
        $checkStmt = $conn->prepare("SELECT Annotated_Image_Path FROM image_uploaded WHERE Image_ID = ?");
        $checkStmt->bind_param("s", $testImageId);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();
        $row = $checkResult->fetch_assoc();
        
        if ($row && $row['Annotated_Image_Path']) {
            echo "<p>✅ Annotated image path saved: " . htmlspecialchars($row['Annotated_Image_Path']) . "</p>";
        } else {
            echo "<p>⏳ Processing may still be in progress. Check again in a few moments.</p>";
        }
    } else {
        echo "<p>❌ Failed to trigger processing. HTTP Code: $httpCode</p>";
    }
    
} else {
    echo "<p>❌ Failed to add test image: " . $conn->error . "</p>";
}

echo "<br><a href='check_annotated_paths.php'>🔍 Check Annotated Paths</a>";
echo "<br><a href='dashboard_mysql.php'>📊 View Dashboard</a>";
echo "<br><a href='overview.php'>👁️ View Overview</a>";

$conn->close();
?>
