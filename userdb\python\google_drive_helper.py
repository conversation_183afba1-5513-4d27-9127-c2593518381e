"""
Google Drive Helper for uploading annotated images
"""
import os
import io
from googleapiclient.discovery import build
from googleapiclient.http import MediaIoBaseUpload
from google.oauth2.service_account import Credentials

class GoogleDriveUploader:
    def __init__(self, credentials_file=None, folder_id=None):
        """
        Initialize Google Drive uploader
        
        Args:
            credentials_file: Path to service account JSON file
            folder_id: Google Drive folder ID for "annotated drone image" folder
        """
        self.credentials_file = credentials_file or "service_account.json"
        self.folder_id = folder_id
        self.service = None
        
    def authenticate(self):
        """Authenticate with Google Drive API"""
        try:
            if os.path.exists(self.credentials_file):
                # Use service account authentication
                credentials = Credentials.from_service_account_file(
                    self.credentials_file,
                    scopes=['https://www.googleapis.com/auth/drive.file']
                )
                self.service = build('drive', 'v3', credentials=credentials)
                print("✅ Google Drive authenticated successfully")
                return True
            else:
                print(f"❌ Credentials file not found: {self.credentials_file}")
                return False
        except Exception as e:
            print(f"❌ Google Drive authentication failed: {e}")
            return False
    
    def upload_image(self, image_data, filename, folder_id=None):
        """
        Upload image to Google Drive
        
        Args:
            image_data: Image data as bytes
            filename: Name for the uploaded file
            folder_id: Specific folder ID (optional)
            
        Returns:
            Google Drive file ID if successful, None if failed
        """
        if not self.service:
            if not self.authenticate():
                return None
        
        try:
            # Use provided folder_id or default
            target_folder = folder_id or self.folder_id
            
            # Create file metadata
            file_metadata = {
                'name': filename,
                'parents': [target_folder] if target_folder else []
            }
            
            # Create media upload
            media = MediaIoBaseUpload(
                io.BytesIO(image_data),
                mimetype='image/jpeg',
                resumable=True
            )
            
            # Upload file
            file = self.service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id'
            ).execute()
            
            file_id = file.get('id')
            print(f"✅ Uploaded {filename} to Google Drive: {file_id}")
            
            # Make file publicly viewable
            self.make_public(file_id)
            
            return file_id
            
        except Exception as e:
            print(f"❌ Failed to upload {filename}: {e}")
            return None
    
    def make_public(self, file_id):
        """Make uploaded file publicly viewable"""
        try:
            permission = {
                'type': 'anyone',
                'role': 'reader'
            }
            self.service.permissions().create(
                fileId=file_id,
                body=permission
            ).execute()
            print(f"✅ Made file {file_id} publicly accessible")
        except Exception as e:
            print(f"⚠️ Failed to make file public: {e}")
    
    def get_public_url(self, file_id):
        """Get public URL for uploaded file"""
        return f"https://drive.google.com/uc?export=view&id={file_id}"

# Simple fallback uploader (saves locally if Google Drive fails)
class LocalFallbackUploader:
    def __init__(self, local_folder="static/annotated"):
        self.local_folder = local_folder
        os.makedirs(local_folder, exist_ok=True)
    
    def upload_image(self, image_data, filename, folder_id=None):
        """Save image locally as fallback"""
        try:
            file_path = os.path.join(self.local_folder, filename)
            with open(file_path, 'wb') as f:
                f.write(image_data)
            print(f"✅ Saved locally: {file_path}")
            return f"local:{filename}"
        except Exception as e:
            print(f"❌ Local save failed: {e}")
            return None
    
    def get_public_url(self, file_id):
        """Get local URL"""
        if file_id.startswith("local:"):
            filename = file_id.replace("local:", "")
            return f"static/annotated/{filename}"
        return None
