#!/usr/bin/env python3
"""
Quick script to check if there are images missing annotated versions
"""

import mysql.connector

DB_CONFIG = {
    'host': 'localhost', 'user': 'root', 'password': '',
    'database': 'userdb', 'charset': 'utf8mb4', 'autocommit': True
}

def main():
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Check for images without annotated paths
        cursor.execute("""
            SELECT COUNT(*) as missing_count
            FROM image_uploaded 
            WHERE Annotated_Image_Path IS NULL
        """)
        missing_count = cursor.fetchone()[0]
        
        # Check for images with annotated paths
        cursor.execute("""
            SELECT COUNT(*) as annotated_count
            FROM image_uploaded 
            WHERE Annotated_Image_Path IS NOT NULL
        """)
        annotated_count = cursor.fetchone()[0]
        
        print(f"📊 Annotation Status:")
        print(f"   ✅ Images with annotations: {annotated_count}")
        print(f"   ❌ Images missing annotations: {missing_count}")
        
        if missing_count > 0:
            print(f"\n⚠️  Run 'python process_images_manual.py' to process missing annotations")
        else:
            print(f"\n🎉 All images have annotations!")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
