/* CSS VARIABLES & RESET */

:root {
  --primary-blue: #1a365d;
  --secondary-blue: #2d5a87;
  --accent-blue: #4299e1;
  --light-blue: #63b3ed;
  --dark-gradient: linear-gradient(135deg, #0f1419 0%, #1a365d 50%, #2d5a87 100%);
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --text-primary: #1a202c;
  --text-secondary: #4a5568;
  --text-light: #718096;
  --surface-white: #ffffff;
  --surface-gray: #f7fafc;
  --shadow-sm: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  overflow-x: hidden;
  background: var(--surface-white);
}

/* LAYOUT & SPACING */

.section {
  padding: 4rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* HERO SECTION */

.hero {
  background: linear-gradient(135deg, rgba(30, 58, 95, 0.85), rgba(74, 144, 226, 0.75)),
            url('https://images.unsplash.com/photo-1509391366360-2e959784a276?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
  color: white;
  padding: 8rem 0 4rem;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('https://images.unsplash.com/photo-1509391366360-2e959784a276?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
  background-size: cover;
  background-position: center;
  opacity: 0.15;
  z-index: 1;
}

.hero-content {
  max-width: 900px;
  padding: 0 2rem;
  position: relative;
  z-index: 3;
  margin-bottom: 3rem;
}

.welcome-text {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 1rem;
  font-weight: 400;
  letter-spacing: 4px;
  text-transform: uppercase;
  color: white;
  animation: fadeInUp 1s ease 0.2s both;
}

.hero h1 {
  font-size: 5rem;
  font-weight: 800;
  margin-bottom: 2rem;
  letter-spacing: 4px;
  background: linear-gradient(135deg, #ffffff, #90cdf4, #63b3ed);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: fadeInUp 1s ease 0.4s both;
  text-shadow: 0 0 30px rgba(66, 153, 225, 0.5);
}

.hero p {
  font-size: 1.3rem;
  margin-bottom: 3rem;
  opacity: 0.9;
  font-weight: 300;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.8;
  animation: fadeInUp 1s ease 0.6s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.cta-button {
  display: inline-block;
  background: linear-gradient(135deg, #4299e1, #2d5a87);
  color: white;
  padding: 1.25rem 3rem;
  text-decoration: none;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: 1px;
  text-transform: uppercase;
  position: relative;
  overflow: hidden;
  animation: fadeInUp 1s ease 0.8s both;
}

.cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.cta-button:hover::before {
  left: 100%;
}

.cta-button:hover {
  background: linear-gradient(135deg, #63b3ed, #4299e1);
  color: white;
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(66, 153, 225, 0.4);
  border-color: rgba(255, 255, 255, 0.6);
}

.hero-waves {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none"><path d="M0,60 C150,90 350,30 600,60 C850,90 1050,30 1200,60 L1200,120 L0,120 Z" fill="white"/></svg>');
  background-size: cover;
  z-index: 3;
}

/* SECTION TITLES */

.section-title {
  font-size: 2.5rem;
  color: #1e3a5f;
  margin-bottom: 1rem;
  font-weight: 700;
  letter-spacing: 1px;
  text-align: center;
}

.section-subtitle {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
}

/* SERVICE VISUALS SECTION */

.service-visuals {
  background: white;
}

.visuals-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  gap: 2rem;
  margin-top: 3rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.visual-card {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  position: relative;
}

.visual-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #4a90e2, #2563eb);
  z-index: 1;
}

.visual-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.12);
}

.visual-media {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 1.5rem;
  position: relative;
}

.visual-media img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.visual-card:hover .visual-media img {
  transform: scale(1.05);
}

.visual-media::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(66, 153, 225, 0.1), rgba(30, 58, 95, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.visual-card:hover .visual-media::after {
  opacity: 1;
}

.visual-card h3 {
  font-size: 1.2rem;
  color: #1e3a5f;
  margin-bottom: 1rem;
  font-weight: 600;
}

.visual-card p {
  color: #666;
  line-height: 1.6;
  font-size: 0.9rem;
}

/* ANIMATIONS */

.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* SCROLLBAR */

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #4299e1, #2d5a87);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #63b3ed, #4299e1);
}

/* RESPONSIVE - TABLET */

@media (max-width: 1024px) {
  .hero h1 {
    font-size: 4rem;
  }
}

/* RESPONSIVE - MOBILE */

@media (max-width: 768px) {
  .section {
    padding: 3rem 0;
  }

  .header-container {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-links {
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
  }

  .login-button {
    margin-top: 0.5rem;
  }

  .hero {
    padding: 6rem 0 3rem;
    min-height: 90vh;
  }

  .hero h1 {
    font-size: 2.5rem;
    letter-spacing: 2px;
  }

  .hero p {
    font-size: 1.1rem;
  }

  .welcome-text {
    font-size: 1rem;
    letter-spacing: 2px;
  }

  .cta-button {
    padding: 1rem 2rem;
    font-size: 1rem;
  }

  .visuals-grid {
    grid-template-columns: 1fr;
    max-width: 400px;
  }

  .footer-container {
    grid-template-columns: 1fr;
    text-align: center;
  }

  footer {
    padding: 3rem 0 2rem;
  }
}

/* RESPONSIVE - SMALL MOBILE */

@media (max-width: 480px) {
  .hero h1 {
    font-size: 2rem;
  }
  
  .cta-button {
    padding: 1rem 2rem;
    font-size: 0.9rem;
  }
}