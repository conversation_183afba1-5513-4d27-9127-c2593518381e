<?php
/**
 * Enhanced Flask Auto-Starter - Ensures Flask app is running and processes images
 */

function isFlaskRunning() {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "http://127.0.0.1:5000/process_new_images");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        error_log("🔍 Flask check error: $error");
    }

    return $httpCode === 200;
}

function killExistingFlask() {
    // Kill any existing Flask processes to avoid port conflicts
    $commands = [
        'taskkill /F /IM python.exe 2>nul',
        'netstat -ano | findstr :5000 | for /f "tokens=5" %a in (\'more\') do taskkill /F /PID %a 2>nul'
    ];

    foreach ($commands as $cmd) {
        exec($cmd, $output, $return_var);
    }

    sleep(1); // Give time for processes to terminate
}

function startFlask() {
    error_log("🚀 Starting Flask application...");

    // Kill existing Flask processes first
    killExistingFlask();

    // Multiple startup methods for better reliability
    $commands = [
        'cd /d "C:\xampp\htdocs\userdb" && start /B python "python function files/app.py"',
        'cd /d "C:\xampp\htdocs\userdb" && start /MIN python "python function files/app.py"',
        'start /D "C:\xampp\htdocs\userdb" /B python "python function files/app.py"',
        'cd /d "C:\xampp\htdocs\userdb" && start /B start_flask.bat'
    ];

    foreach ($commands as $command) {
        error_log("🔄 Trying command: $command");

        // Use exec for better process control
        $output = [];
        $return_var = 0;
        exec($command, $output, $return_var);

        // Wait for Flask to start
        sleep(5);

        // Check if it started successfully with multiple attempts
        $attempts = 0;
        while ($attempts < 20 && !isFlaskRunning()) {
            sleep(1);
            $attempts++;
            if ($attempts % 5 == 0) {
                error_log("⏳ Waiting for Flask... attempt $attempts/20");
            }
        }

        if (isFlaskRunning()) {
            error_log("✅ Flask started successfully with command: $command");
            return true;
        }

        error_log("❌ Command failed: $command (return code: $return_var)");
        if (!empty($output)) {
            error_log("📝 Command output: " . implode("\n", $output));
        }
    }

    error_log("❌ All Flask startup methods failed");
    return false;
}

function triggerImageProcessing() {
    error_log("🎯 Starting image processing trigger...");

    // Always try to start Flask first for maximum reliability
    if (!isFlaskRunning()) {
        error_log("🚀 Flask not running, starting it...");
        if (!startFlask()) {
            error_log("❌ Failed to start Flask app");
            return false;
        }
        error_log("✅ Flask started successfully");
    } else {
        error_log("✅ Flask is already running");
    }

    // Multiple attempts to trigger processing
    $maxAttempts = 3;
    for ($attempt = 1; $attempt <= $maxAttempts; $attempt++) {
        error_log("🔄 Processing attempt $attempt/$maxAttempts");

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "http://127.0.0.1:5000/process_new_images");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // Increased timeout for ML processing
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            error_log("❌ cURL error on attempt $attempt: $error");
            if ($attempt < $maxAttempts) {
                sleep(2); // Wait before retry
                continue;
            }
        }

        if ($httpCode === 200) {
            $result = json_decode($response, true);
            $status = $result['status'] ?? 'Unknown';
            error_log("🎯 Processing successful on attempt $attempt: $status");
            error_log("📊 Response: " . substr($response, 0, 200) . "...");
            return true;
        } else {
            error_log("❌ HTTP error on attempt $attempt: $httpCode");
            if ($attempt < $maxAttempts) {
                sleep(2); // Wait before retry
            }
        }
    }

    error_log("❌ All processing attempts failed");
    return false;
}

// If called directly, just trigger processing
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    header('Content-Type: application/json');
    
    $success = triggerImageProcessing();
    
    echo json_encode([
        'status' => $success ? 'success' : 'error',
        'flask_running' => isFlaskRunning(),
        'message' => $success ? 'Processing triggered successfully' : 'Failed to trigger processing'
    ]);
}
?>
