@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', 'Segoe UI', sans-serif;
  background-color: #f8fafc;
  min-height: 100vh;
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: auto;
  padding: 2rem;
  height: calc(100vh - 40px);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* IMPROVED HEADER BAR - NOW RESPONSIVE */
.header-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  row-gap: 0.5rem;
}

.header-bar h1 {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.date {
  font-size: 0.9rem;
  color: #6b7280;
}

/* FILTERS SECTION */
.filters-section {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
  align-items: center;
  flex-wrap: nowrap;
  flex-shrink: 0;
  overflow-x: auto;
}

.filters-grid {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  flex-wrap: nowrap;
  flex: 1;
  min-width: 0;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
  white-space: nowrap;
}

.filter-group label {
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
  flex-shrink: 0;
}

select, input[type="date"], input[type="text"] {
  padding: 0.4rem 0.6rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.85rem;
  transition: all 0.3s ease;
  background: white;
  min-width: 120px;
  max-width: 160px;
  height: 32px;
  flex-shrink: 0;
}

select:focus, input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-container {
  position: relative;
}

.search-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 1rem;
}

.apply-btn {
  background: #3b82f6;
  color: white;
  border: 1px solid #3b82f6;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.85rem;
  height: 32px;
}

.apply-btn:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.pdf-download-btn {
  background: #10b981;
  color: white;
  border: 1px solid #10b981;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.85rem;
  height: 32px;
}

.pdf-download-btn:hover {
  background: #059669;
  border-color: #059669;
}

.pdf-download-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-shrink: 0;
}

/* METRICS GRID */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.75rem;
  margin-top: 0.75rem;
  margin-bottom: 1.5rem;
  flex-shrink: 0;
}

.metric-card {
  text-align: center;
  padding: 0.6rem;
  background: white;
  border-radius: 6px;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.metric-card h3 {
  font-size: 1rem;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 0.4rem;
  line-height: 1.2;
}

.metric-label {
  color: #6b7280;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1.2;
  margin-bottom: 0.4rem;
  text-transform: capitalize;
}

.metric-value {
  font-size: 1.6rem;
  font-weight: 700;
  margin-bottom: 0.2rem;
  line-height: 1.1;
}

.metric-card.common-defect .metric-value,
.metric-card.maintenance .metric-value,
.metric-card.common-maintenance .metric-value {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.2;
}

.metric-sublabel {
  color: #64748b;
  font-size: 0.75rem;
  margin-top: 0.25rem;
  font-weight: 400;
}

.metric-change {
  font-size: 0.75rem;
  color: #64748b;
}

.metric-change.positive {
  color: #10b981;
}

.metric-change.negative {
  color: #ef4444;
}

/* Color coding for metrics */
.metric-card.total-inspected .metric-value, .metric-card.total .metric-value { color: #3b82f6; }
.metric-card.passed .metric-value, .metric-card.healthy .metric-value { color: #10b981; }
.metric-card.failed .metric-value, .metric-card.defect .metric-value, .metric-card.defective .metric-value { color: #ef4444; }
.metric-card.efficiency .metric-value, .metric-card.rate .metric-value { color: #f59e0b; }
.metric-card.common-defect .metric-value { color: #8b5cf6; }
.metric-card.maintenance .metric-value { color: #f59e0b; }
.metric-card.common-maintenance .metric-value { color: #06b6d4; }
.metric-card.severity .metric-value { color: #06b6d4; }

.maintenance-yes { color: #ef4444; }
.maintenance-no { color: #10b981; }
.severity-low { color: #06b6d4; }
.severity-medium { color: #f59e0b; }
.severity-high { color: #ef4444; }

/* IMAGES SECTION */
.images-section {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  border: 1px solid #e5e7eb;
  flex-shrink: 0;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.images-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.container-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
  text-align: left;
  align-self: flex-start;
}

.image-container {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.image-container .container-title {
  align-self: flex-start;
  margin-bottom: 1rem;
}

.image-container .image-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.image-container .image-content img:not(.annotated-image) {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
}

.image-preview, .annotated-image {
  width: 250px;
  height: 250px;
  border-radius: 8px;
  object-fit: contain;
  transition: opacity 0.3s ease;
  margin: 0 auto;
  border: 2px solid #22d3ee;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

#imageContent {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  flex: 1;
  color: #64748b;
  font-size: 0.875rem;
}

.no-image-placeholder {
  width: 100%;
  height: 180px;
  background: #f8fafc;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 3rem;
  margin-bottom: 0;
}

.image-preview.placeholder {
  object-fit: contain;
}

.image-preview.loaded {
  object-fit: cover;
}

.annotated-image {
  width: 250px !important;
  height: 250px !important;
  object-fit: contain !important;
  max-width: 250px !important;
  max-height: 250px !important;
}

.image-preview.loading {
  opacity: 0.6;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.image-preview.loaded {
  opacity: 1;
}

.image-preview.error {
  opacity: 0.5;
  background: #fee;
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #64748b;
}

.image-icon {
  width: 60px;
  height: 60px;
  border: 3px solid #94a3b8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  position: relative;
  background: white;
}

.image-icon::before {
  content: '';
  width: 20px;
  height: 16px;
  background: #94a3b8;
  border-radius: 3px 3px 0 0;
  position: relative;
}

.image-icon::after {
  content: '';
  position: absolute;
  width: 6px;
  height: 6px;
  background: #94a3b8;
  border-radius: 50%;
  top: 12px;
  right: 12px;
}

.magnifier {
  position: absolute;
  bottom: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  border: 2px solid #94a3b8;
  border-radius: 50%;
  background: white;
}

.magnifier::after {
  content: '';
  position: absolute;
  bottom: -8px;
  right: -8px;
  width: 8px;
  height: 2px;
  background: #94a3b8;
  transform: rotate(45deg);
  border-radius: 1px;
}

/* IMPROVED DASHBOARD GRID - NOW FULLY RESPONSIVE */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  transition: grid-template-columns 0.3s ease;
}

.image-section {
  grid-column: 1;
  grid-row: 1;
}

.panel-health-section {
  grid-column: 2;
  grid-row: 1;
}

.defect-frequency-section {
  grid-column: 1;
  grid-row: 2;
}

.defect-distribution-section {
  grid-column: 2;
  grid-row: 2;
}

.chart-container {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.chart-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #1e293b;
  text-align: left;
  flex-shrink: 0;
}

.chart-subtitle {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0 0 1rem 0;
  text-align: left;
  flex-shrink: 0;
}

.chart-canvas {
  flex: 1;
  max-height: 220px;
  height: auto;
  min-height: 180px;
  margin: 0;
  position: relative;
}

.defect-summary-table {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  flex: 1;
}

.defect-summary-table table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.defect-summary-table th {
  background: #f8fafc;
  color: #374151;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 0.75rem;
  text-align: center;
  border-bottom: 2px solid #e5e7eb;
}

.defect-summary-table td {
  padding: 1rem 0.75rem;
  text-align: center;
  font-size: 0.9rem;
  font-weight: 500;
  border-bottom: 1px solid #f1f5f9;
}

.defect-summary-table tr:last-child td {
  border-bottom: none;
}

.defect-summary-table tr:hover {
  background: #f8fafc;
}

.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  background: #fef2f2;
  color: #dc2626;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem;
  border: 1px solid #fecaca;
}

.chart-placeholder {
  height: 200px;
  background: #f8fafc;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  font-size: 1rem;
  border: 1px solid #e5e7eb;
  width: 100%;
}

.card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1e293b;
}

.card-subtitle {
  font-size: 0.875rem;
  color: #64748b;
  margin-bottom: 1rem;
}

/* RESPONSIVE BREAKPOINTS */
@media (max-width: 1024px) {
  .filters-section {
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .filters-grid {
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .action-buttons {
    margin-left: 0;
    flex-basis: 100%;
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  body { 
    padding: 1rem; 
  }
  
  .container {
    padding: 1rem;
    height: auto;
    min-height: calc(100vh - 40px);
  }
  
  .header-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .header-bar h1 {
    white-space: normal;
    font-size: 1.5rem;
  }
  
  .filters-section { 
    flex-direction: column; 
    align-items: stretch; 
    padding: 1rem;
    gap: 1rem;
  }
  
  .filters-grid { 
    flex-direction: column; 
    gap: 1rem;
    width: 100%;
  }

  .filter-group {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .filter-group select, .filter-group input {
    width: 100%;
    min-width: auto;
  }

  .search-container {
    flex-direction: column;
    align-items: stretch;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.5rem;
    margin-left: 0;
  }

  .apply-btn, .pdf-download-btn {
    width: 100%;
    justify-content: center;
  }

  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  .metric-card {
    min-height: 60px;
    padding: 0.3rem;
  }

  .metric-card h3 { font-size: 0.65rem; }
  .metric-value { font-size: 1.2rem; }
  .metric-card.common-defect .metric-value,
  .metric-card.maintenance .metric-value,
  .metric-card.common-maintenance .metric-value {
    font-size: 0.8rem;
  }

  /* DASHBOARD GRID STACKS VERTICALLY ON MOBILE */
  .dashboard-grid {
    grid-template-columns: 1fr;
    grid-auto-rows: auto;
    gap: 1rem;
  }

  /* Reset grid positions for mobile */
  .image-section,
  .panel-health-section,
  .defect-frequency-section,
  .defect-distribution-section {
    grid-column: 1;
    grid-row: auto;
  }

  .images-grid { 
    grid-template-columns: 1fr; 
    gap: 1rem;
  }
    
  .chart-container,
  .image-container {
    min-height: auto;
  }
}

/* Tablet-specific adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
  .dashboard-grid {
    gap: 1rem;
  }
  
  .chart-container {
    padding: 1rem;
  }
}