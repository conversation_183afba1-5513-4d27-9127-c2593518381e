<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Dronlytics Images Selection Section</title>
  <link rel="stylesheet" href="../CSS/Dronlytics Images Selection.css"> 
</head>
<body>

  <!-- === HEADER === -->
  <?php include '../PHP/Dronlytics Header After Login.php'; ?>

  <!-- === MAIN CONTENT === -->
  <div class="main-content">
    <div class="select-container">
      <h2 class="page-title">Image Selection</h2>
      <div id="projectNameDisplay">Project: Loading...</div>

      <!-- SELECTION TOOLS -->
      <div class="selection-tools">
        <div class="select-all-container">
          <label>
            <input type="checkbox" id="selectAllCheckbox" /> Select All Images
          </label>
        </div>
        <div class="select-all-container">
          <span id="selectionCount">0 Images Selected</span>
        </div>
      </div>

      <!-- IMAGE GRID -->
      <div class="image-grid" id="imageGrid"></div>

      <!-- PROCEED BUTTON -->
      <button class="proceed-button" id="siteBtn">Select and Proceed</button>
    </div>
  </div>

  <!-- === FOOTER === -->
  <?php include '../PHP/Dronlytics Footer V2.php'; ?>


  <!-- === JAVASCRIPT === -->
  <script>
    // === 1. DISPLAY PROJECT NAME FROM SESSIONSTORAGE ===
    const projectName = sessionStorage.getItem("projectName") || "Unnamed Project";
    document.getElementById("projectNameDisplay").textContent = "Project: " + projectName;

    // === 2. LOAD EXTRACTED IMAGES FROM ZIP IN SESSIONSTORAGE ===
    const extractedImages = JSON.parse(sessionStorage.getItem("extractedZipImages") || "[]");

    // === 3. DOM ELEMENT REFERENCES ===
    const grid = document.getElementById("imageGrid");
    const selectAll = document.getElementById("selectAllCheckbox");
    const selectionCount = document.getElementById("selectionCount");

    // === 4. UPDATE SELECTION COUNT & ENABLE/DISABLE PROCEED BUTTON ===
    function updateSelectionCount() {
      const selectedCount = document.querySelectorAll(".imageCheckbox:checked").length;
      selectionCount.textContent = `${selectedCount} Image${selectedCount !== 1 ? 's' : ''} Selected`;
      document.getElementById("siteBtn").disabled = selectedCount === 0;
    }

    // === 5. RENDER IMAGE CHECKBOXES INTO THE GRID ===
    extractedImages.forEach((imgObj, index) => {
      const box = document.createElement("div");
      box.className = "image-box";

      const img = document.createElement("img");
      img.src = imgObj.data;

      const checkbox = document.createElement("input");
      checkbox.type = "checkbox";
      checkbox.className = "imageCheckbox";
      checkbox.dataset.index = index;

      // INDIVIDUAL IMAGE CHECKBOX CHANGE EVENT
      checkbox.addEventListener("change", () => {
        updateSelectionCount();

        // SYNC "SELECT ALL" CHECKBOX IF ALL ARE CHECKED
        const allChecked = document.querySelectorAll(".imageCheckbox").length ===
                           document.querySelectorAll(".imageCheckbox:checked").length;
        selectAll.checked = allChecked;
      });

      box.appendChild(checkbox);
      box.appendChild(img);
      grid.appendChild(box);
    });

    // === 6. HANDLE "SELECT ALL" CHECKBOX TOGGLE ===
    selectAll.addEventListener("change", () => {
      document.querySelectorAll(".imageCheckbox").forEach(cb => {
        cb.checked = selectAll.checked;
      });
      updateSelectionCount();
    });

    // INITIAL COUNT ON LOAD
    updateSelectionCount();

    // === 7. PROCEED BUTTON: STORE SELECTED IMAGES AND REDIRECT ===
    document.getElementById("siteBtn").addEventListener("click", () => {
      const selected = [];

      document.querySelectorAll(".imageCheckbox:checked").forEach(cb => {
        const index = parseInt(cb.dataset.index);
        selected.push(extractedImages[index]);
      });

      if (selected.length === 0) {
        alert("Please select at least one image.");
        return;
      }

      console.log(`✅ Selected ${selected.length} images for upload`);
      sessionStorage.setItem("finalizedImages", JSON.stringify(selected));
      window.location.href = "../PHP/Dronlytics Image List.php";
    });

    // === 8. OPTIONAL: REDIRECT FUNCTION WITH USER SESSION CHECK ===
    function redirectTo(targetPage) {
      const userId = sessionStorage.getItem("loggedInUserID");
      if (!userId) {
        alert("User session expired. Please login again.");
        window.location.href = "../PHP/Dronlytics Login Page.php";
        return;
      }
      window.location.href = `${targetPage}?user=${encodeURIComponent(userId)}`;
    }
  </script>

</body>
</html>
