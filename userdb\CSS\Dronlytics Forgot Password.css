/* === GOOGLE FONT === */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* === GLOBAL RESET === */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  overflow-x: hidden;
}


/* === FORGOT PASSWORD SECTION === */
.forgot-password-section {
  background: linear-gradient(135deg, rgba(30, 58, 95, 0.75), rgba(74, 144, 226, 0.65)),
              url('https://images.unsplash.com/photo-1473968512647-3e447244af8f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
  background-size: cover;
  background-position: center;
  height: calc(100vh - 120px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  margin-top: 60px;
  margin-bottom: 0;
}

.forgot-password-container {
  max-width: 380px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: auto;
}

.forgot-password-box {
  background: white;
  padding: 1rem 1rem;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  text-align: left;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  width: 100%;
  margin: auto;
}

.forgot-password-box h2 {
  font-size: 1.2rem;
  color: #1e3a5f;
  margin-bottom: 0.4rem;
  font-weight: 600;
  text-align: center;
}

.forgot-password-subtitle {
  color: #64748b;
  font-size: 0.8rem;
  text-align: center;
  margin-bottom: 0.8rem;
  line-height: 1.3;
}

/* === FORM ELEMENTS === */
.form-group {
  margin-bottom: 0.4rem;
  text-align: left;
}

.form-group label {
  display: none;
}

.form-group input {
  width: 100%;
  padding: 0.6rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.85rem;
  transition: all 0.3s ease;
  background: white;
  color: #333;
  font-family: 'Inter', sans-serif;
}

.form-group input::placeholder {
  color: #a0aec0;
  font-size: 0.95rem;
}

.form-group input:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  transform: translateY(-1px);
}

/* === BUTTONS === */
.reset-button {
  width: 100%;
  background: linear-gradient(135deg, #4299e1, #2d5a87);
  color: white;
  padding: 0.5rem 2rem;
  border: none;
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 0.4rem;
  font-family: 'Inter', sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.reset-button:hover {
  background: linear-gradient(135deg, #63b3ed, #4299e1);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(66, 153, 225, 0.3);
}

.reset-button:active {
  transform: translateY(0px);
}

/* === FORM LINKS === */
.form-links {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
  margin-top: 0.4rem;
}

.back-to-login-link,
.signup-link {
  color: #4299e1;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.85rem;
  display: block;
  text-align: center;
  transition: all 0.3s ease;
}

.back-to-login-link:hover,
.signup-link:hover {
  color: #2d5a87;
  text-decoration: underline;
}

/* === SUCCESS MESSAGE === */
.success-message {
  background: #d4edda;
  color: #155724;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  text-align: center;
  border: 1px solid #c3e6cb;
  display: none;
}

.success-message.show {
  display: block;
}

/* === FOOTER === */
footer {
  background: #1e3a5f;
  color: white;
  text-align: center;
  padding: 0.75rem 0;
  position: relative;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}


/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
  .forgot-password-section {
    padding: 0.5rem;
    height: calc(100vh - 110px);
    margin-top: 50px;
    margin-bottom: 0;
  }

  .forgot-password-container {
    min-height: auto;
    max-width: 350px;
  }

  .forgot-password-box {
    padding: 0.8rem 0.6rem;
  }

  .forgot-password-box h2 {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .forgot-password-section {
    height: calc(100vh - 110px);
    padding: 0.3rem;
  }

  .forgot-password-container {
    max-width: 320px;
  }

  .forgot-password-box {
    padding: 0.7rem 0.5rem;
  }

  .forgot-password-box h2 {
    font-size: 1rem;
  }

  .forgot-password-subtitle {
    font-size: 0.75rem;
    margin-bottom: 0.6rem;
  }

  .form-group input {
    padding: 0.5rem 0.8rem;
    font-size: 0.8rem;
  }

  .reset-button {
    padding: 0.45rem 1.5rem;
    font-size: 0.8rem;
  }
}