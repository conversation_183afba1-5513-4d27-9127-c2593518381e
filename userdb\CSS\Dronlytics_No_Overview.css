/* === GOOGLE FONTS IMPORT === */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* === GLOBAL RESET === */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* === BODY STYLING === */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  overflow: hidden;
  background: rgba(74, 144, 226, 0.03);
  height: 100vh;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
}

/* === HTML SCROLL BEHAVIOR === */
html {
  scroll-behavior: smooth;
}


/* === MAIN CONTENT  === */
.main-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 2rem;
  background: rgba(74, 144, 226, 0.05);
  position: relative;
  transform: translateY(30px);
}


/* === OVERVIEW CONTAINER === */
.overview-container {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.08);
  text-align: center;
  max-width: 600px;
  width: 100%;
  border: 1px solid #f0f0f0;
  position: relative;
  overflow: hidden;
}

.overview-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4a90e2, #002f5f);
}


/* === OVERVIEW TITLE AND SUBTITLE === */
.overview-title {
  font-size: 1.5rem;
  color: #1e3a5f;
  margin-bottom: 0.8rem;
  font-weight: 700;
  letter-spacing: 1px;
}

.overview-subtitle {
  font-size: 0.95rem;
  color: #666;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}


/* === STATUS MESSAGE === */
.status-message {
  background: rgba(255, 193, 7, 0.1);
  border: 2px solid rgba(255, 193, 7, 0.3);
  border-radius: 12px;
  padding: 1.3rem;
  margin-bottom: 1.3rem;
}


/* === STATUS ICON === */
.status-icon {
  font-size: 2.2rem;
  margin-bottom: 0.6rem;
}


/* === STATUS TEXT === */
.status-text {
  font-size: 1.1rem;
  color: #1e3a5f;
  font-weight: 600;
  margin-bottom: 0.6rem;
}


/* === STATUS DESCRIPTION === */
.status-description {
  font-size: 0.85rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.2rem;
}


/* === ACTION BUTTONS === */
.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}


/* === PRIMARY BUTTON === */
.btn-primary {
  background: linear-gradient(135deg, #4a90e2, #002f5f);
  color: white;
  font-weight: 600;
  padding: 0.9rem 1.8rem;
  border: none;
  border-radius: 8px;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(74, 144, 226, 0.3);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(74, 144, 226, 0.4);
}


/* === HEADER AND FOOTER BALANCE === */
header {
  flex-shrink: 0;
}

footer {
  flex-shrink: 0;
}

/* === RESPONSIVE BEHAVIOR === */
@media (max-width: 1024px) {
  /* allow scrolling on mobile */
  html, body {
    height: auto !important;
    min-height: 100vh;
    overflow-x: hidden;
    overflow-y: auto !important;           /* was hidden */
    -webkit-overflow-scrolling: touch;     /* smooth iOS scroll */
  }

  /* give space under fixed header and above footer */
  .main-content {
    /* cancel the upward shift so it doesn't get clipped */
    transform: none !important;            /* was translateY(30px) */
    padding: 2rem 1rem;                    /* extra space top/bottom */
    padding-top: calc(64px + 16px);        /* header(64) + breathing room */
    padding-bottom: 0.5cm;                 /* space before footer in cm */
  }

  .overview-container {
    padding: 1.5rem;
  }
}

/* Small phones */
@media (max-width: 480px) {
  .main-content {
    padding-top: calc(64px + 20px);        /* a bit more room under header */
    padding-bottom: 0.5cm;                 /* same space above footer */
  }
}
