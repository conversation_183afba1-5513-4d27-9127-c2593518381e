<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dronlytics - Template</title>
    <link rel="stylesheet" href="../CSS/Dronlytics Long Footer.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
    </style>
</head>
<body>

    <!--- FOOTER --->
    <footer id="contact">
        <div class="footer-container">
            <div class="footer-left">
                <h2>DRONLYTICS</h2>
                <p><strong>HEADQUARTERS</strong></p>
                <p>
                    Menara Sunway, Jalan Lagoon Timur,<br>
                    Sunway City, 47500, Selangor D.E.<br><br>
                    📧 <EMAIL><br>
                    📱 +60 123-456-789
                </p>
            </div>

            <div class="footer-middle">
                <h3>STAY UPDATED</h3>
                <p>
                    By signing up, you agree to our <a href="#terms">Terms & Conditions</a> 
                    and have read our <a href="#privacy">Privacy Notice</a>.
                </p>
                <div class="email-subscribe">
                    <input type="email" placeholder="Enter your email address" aria-label="Email address">
                    <button type="button" aria-label="Subscribe">→</button>
                </div>
            </div>

            <div class="footer-right">
                <h3>FOLLOW US</h3>
                <div class="social-icons">
                    <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" aria-label="Facebook">
                        <svg viewBox="0 0 24 24" fill="currentColor" width="20" height="20">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                    </a>
                    <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" aria-label="Instagram">
                        <svg viewBox="0 0 24 24" fill="currentColor" width="20" height="20">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                    </a>
                    <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer" aria-label="LinkedIn">
                        <svg viewBox="0 0 24 24" fill="currentColor" width="20" height="20">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </footer>


    <!--- JAVASCRIPT --->
    <script>
        // DROPDOWN FUNCTIONALITY
        function toggleDropdown(id) {
            // Close all other dropdowns first
            document.querySelectorAll('.dropdown').forEach(el => {
                if (el.id !== id) el.classList.remove('show');
            });
            
            // Toggle the clicked dropdown
            const dropdown = document.getElementById(id);
            if (dropdown) {
                dropdown.classList.toggle("show");
            }
        }

        // Close dropdowns when clicking outside
        document.addEventListener("click", function(e) {
            const dropdowns = document.querySelectorAll(".dropdown");
            dropdowns.forEach(dropdown => {
                if (!dropdown.contains(e.target)) {
                    dropdown.classList.remove("show");
                }
            });
        });

        // DOM CONTENT LOADED EVENT HANDLER
        document.addEventListener('DOMContentLoaded', function() {
            // Add hover functionality for dropdowns
            const dropdowns = document.querySelectorAll('.dropdown');
            
            dropdowns.forEach(dropdown => {
                dropdown.addEventListener('mouseenter', function() {
                    this.classList.add('show');
                });
                
                dropdown.addEventListener('mouseleave', function() {
                    this.classList.remove('show');
                });
            });

            // Handle email subscription
            const subscribeBtn = document.querySelector('.email-subscribe button');
            const emailInput = document.querySelector('.email-subscribe input');
            
            if (subscribeBtn) {
                subscribeBtn.addEventListener('click', function() {
                    const email = emailInput.value.trim();
                    
                    if (email && email.includes('@')) {
                        alert('Thank you for subscribing!');
                        emailInput.value = '';
                    } else {
                        alert('Please enter a valid email address.');
                    }
                });
            }

            if (emailInput) {
                emailInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        subscribeBtn.click();
                    }
                });
            }
        });

        // REDIRECT FUNCTION
        function redirectTo(targetPage) {
            const userId = sessionStorage.getItem("loggedInUserID");
            if (!userId) {
                alert("User session expired. Please login again.");
                window.location.href = "../PHP/Dronlytics Login Page.php";
                return;
            }
            window.location.href = `${targetPage}?user=${encodeURIComponent(userId)}`;
        }

        // Handle messages from iframe content
        window.addEventListener('message', function(event) {
            if (event.data.action === 'navigate') {
                window.location.href = event.data.url;
            }
        });
    </script>
    
</body>
</html>
