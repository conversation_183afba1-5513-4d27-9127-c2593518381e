#!/usr/bin/env python3
"""
Test script to check what your model actually detects
Run this to see your model's output format
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ultralytics import YOLO
import cv2

def test_model():
    try:
        print("🔍 Loading model...")
        model = YOLO("best1.pt")
        
        print("📋 Model info:")
        print(f"   Classes: {model.names}")
        print(f"   Number of classes: {len(model.names)}")
        
        # Test with a sample image if available
        test_image_path = input("Enter path to test image (or press Enter to skip): ").strip()
        
        if test_image_path and os.path.exists(test_image_path):
            print(f"🖼️ Testing with image: {test_image_path}")
            
            results = model(test_image_path)
            result = results[0]
            
            print(f"📊 Detection results:")
            print(f"   Number of detections: {len(result.boxes) if result.boxes else 0}")
            
            if result.boxes:
                for i, box in enumerate(result.boxes):
                    cls = int(box.cls[0])
                    conf = float(box.conf[0])
                    class_name = model.names[cls]
                    print(f"   Detection {i+1}: {class_name} (confidence: {conf:.3f})")
            else:
                print("   No detections found")
                
        else:
            print("⏭️ Skipping image test")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print("💡 Make sure you have:")
        print("   1. ultralytics installed: pip install ultralytics")
        print("   2. best1.pt file in the model directory")
        print("   3. Valid model file")

if __name__ == "__main__":
    test_model()
