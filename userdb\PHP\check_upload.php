<?php
session_start();
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['status' => 'unauthorized']);
    exit;
}

$userId = $_SESSION['user_id'];

try {
    // Database connection
    $conn = new mysqli('localhost', 'root', '', 'userdb');
    
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    // Check if user has uploaded any images
    $stmt = $conn->prepare("SELECT COUNT(*) as image_count FROM image_uploaded WHERE User_ID = ?");
    $stmt->bind_param("s", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    $imageCount = $row['image_count'];
    
    if ($imageCount > 0) {
        echo json_encode(['status' => 'uploaded', 'count' => $imageCount]);
    } else {
        echo json_encode(['status' => 'not_uploaded', 'count' => 0]);
    }
    
    $stmt->close();
    $conn->close();
    
} catch (Exception $e) {
    error_log("Check upload error: " . $e->getMessage());
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}
?>
