<?php
// get_overview_data.php - Enhanced data retrieval for overview dashboard
header("Content-Type: application/json");

$mysqli = new mysqli("localhost", "root", "", "userdb");

if ($mysqli->connect_error) {
    http_response_code(500);
    echo json_encode(["error" => "Database connection failed: " . $mysqli->connect_error]);
    exit();
}

// Get parameters
$user = $_GET['user'] ?? 'U001';
$image_id = $_GET['image_id'] ?? null;
$project = $_GET['project'] ?? null;
$date = $_GET['date'] ?? null;

try {
    // Base query to get all data with image paths and calculate metrics from defects
    $query = "
        SELECT DISTINCT
            iu.Image_ID,
            iu.Project_Name,
            iu.Image_Path,
            iu.Annotated_Image_Path,
            iu.Upload_Timestamp,
            DATE(iu.Upload_Timestamp) as Analysis_Date
        FROM image_uploaded iu
        WHERE iu.User_ID = ? AND iu.Annotated_Image_Path IS NOT NULL
    ";
    
    $params = [$user];
    $types = "s";
    
    // Add filters
    if ($image_id) {
        $query .= " AND iu.Image_ID = ?";
        $params[] = $image_id;
        $types .= "s";
    }
    
    if ($project) {
        $query .= " AND iu.Project_Name LIKE ?";
        $params[] = "%" . $project . "%";
        $types .= "s";
    }
    
    if ($date) {
        $query .= " AND DATE(iu.Upload_Timestamp) = ?";
        $params[] = $date;
        $types .= "s";
    }

    $query .= " ORDER BY iu.Upload_Timestamp DESC, iu.Image_ID ASC";
    
    $stmt = $mysqli->prepare($query);
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $mysqli->error);
    }
    
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $data = [];
    while ($row = $result->fetch_assoc()) {
        // Calculate panel metrics from defects for this image
        $defect_query = "SELECT SUM(Defect_Count) as total_defects FROM image_defects WHERE Image_ID = ?";
        $defect_stmt = $mysqli->prepare($defect_query);
        $defect_stmt->bind_param("s", $row['Image_ID']);
        $defect_stmt->execute();
        $defect_result = $defect_stmt->get_result();
        $defect_data = $defect_result->fetch_assoc();

        // Assume 100 total panels (you can adjust this based on your requirements)
        $total_panels = 100;
        $defect_panels = (int)($defect_data['total_defects'] ?? 0);
        $healthy_panels = max(0, $total_panels - $defect_panels);
        $defective_rate = $total_panels > 0 ? round(($defect_panels / $total_panels) * 100, 2) : 0;

        // Build response with calculated metrics
        $data[] = [
            "Image_ID" => $row["Image_ID"],
            "Project_Name" => $row["Project_Name"],
            "Image_Path" => $row["Image_Path"],
            "Annotated_Image_Path" => $row["Annotated_Image_Path"],
            "Upload_Timestamp" => $row["Upload_Timestamp"],
            "Total_Panel_Count" => $total_panels,
            "Healthy_Panel_Count" => $healthy_panels,
            "Defect_Panel_Count" => $defect_panels,
            "Defective_Rate" => $defective_rate . '%',
            "Analysis_Date" => $row["Analysis_Date"]
        ];

        $defect_stmt->close();
    }
    
    echo json_encode($data);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(["error" => "Query failed: " . $e->getMessage()]);
} finally {
    $mysqli->close();
}
?>
