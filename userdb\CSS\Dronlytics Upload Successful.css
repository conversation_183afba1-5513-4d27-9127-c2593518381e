/* === IMPORT FONT === */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');


/* === GLOBAL RESET & BASE STYLING === */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  overflow: hidden;
  background: rgba(74, 144, 226, 0.03);
  height: 100vh;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
}


/* === MAIN CONTENT SECTION === */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 2rem;
  background: rgba(74, 144, 226, 0.05);
  position: relative;
  transform: translateY(30px);
}

/* === SUCCESS MESSAGE BOX === */
.success-container {
  background: white;
  padding: 4rem 3rem;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.08);
  text-align: center;
  max-width: 600px;
  width: 100%;
  border: 1px solid #f0f0f0;
  position: relative;
  overflow: hidden;
}

.success-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4a90e2, #002f5f);
}

/* === CHECK ICON CIRCLE === */
.check-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #10b981, #059669);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 2rem;
  color: white;
  font-size: 2.5rem;
  box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
}

/* === TITLE AND SUBTITLE === */
.success-title {
  font-size: 2rem;
  color: #1e3a5f;
  margin-bottom: 1rem;
  font-weight: 700;
}

.success-subtitle {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 2.5rem;
}

/* === ACTION BUTTONS === */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.primary-button {
  background: linear-gradient(135deg, #4a90e2, #002f5f);
  color: white;
  font-weight: 600;
  padding: 1rem 2.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  cursor: pointer;
  text-decoration: none;
}

.secondary-button {
  background: transparent;
  color: #4a90e2;
  font-weight: 500;
  padding: 0.75rem 2rem;
  border: 2px solid #4a90e2;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  text-decoration: none;
}


/* === RESPONSIVE BEHAVIOR === */

/* Mobile (0-1024px) - Enable scrolling */
@media (max-width: 1024px) {
  html, body {
    overflow-y: auto; /* Enable scrolling */
    height: auto;
    min-height: 100vh;
  }

  .main-content {
    padding: 0 1rem;
    min-height: 100vh;
  }

  /* Small mobile specific adjustments */
  @media (max-width: 400px) {
    .success-container {
      margin-top: 3rem;
      padding: 2.5rem 1.5rem;
    }
  }

  /* Tablet adjustments */
  @media (min-width: 401px) {
    .success-container {
      padding: 3rem 2rem;
      max-width: 90%;
    }
  }
}

/* Desktop (1025px+) - Lock to single viewport */
@media (min-width: 1025px) {
  html, body {
    overflow: hidden; /* Disable scrolling */
    height: 100vh;
  }

  .main-content {
    padding: 2rem;
    overflow: hidden;
    height: 100vh;
    justify-content: center;
  }

  .success-container {
    padding: 3rem;
    max-width: 450px;
    margin: 0 auto;
  }
}