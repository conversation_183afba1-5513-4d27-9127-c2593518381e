<?php
// overview_api.php - API endpoint for overview dashboard data
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(["error" => "User not logged in"]);
    exit();
}

header("Content-Type: application/json");

$mysqli = new mysqli("localhost", "root", "", "userdb");

if ($mysqli->connect_error) {
    http_response_code(500);
    echo json_encode(["error" => "DB connection failed"]);
    exit();
}

$user = $_SESSION['user_id'];
$api_type = $_GET['api'] ?? 'defect_types';

if ($api_type === 'defect_types') {
    // Get defect types data
    $project = $_GET['project'] ?? null;
    $date = $_GET['date'] ?? null;
    $image_id = $_GET['image_id'] ?? null;

    // Check if Maintenance_Type column exists
    $columnCheck = $mysqli->query("SHOW COLUMNS FROM image_defects LIKE 'Maintenance_Type'");
    $hasMaintenanceColumn = $columnCheck && $columnCheck->num_rows > 0;

    if ($hasMaintenanceColumn) {
        $defectTypesQuery = "
            SELECT
                id.Defect_Type,
                id.Maintenance_Type,
                SUM(id.Defect_Count) as Total_Count
            FROM image_defects id
            INNER JOIN image_uploaded iu ON id.Image_ID = iu.Image_ID
            WHERE iu.User_ID = ?
        ";
    } else {
        $defectTypesQuery = "
            SELECT
                id.Defect_Type,
                'Inspect' as Maintenance_Type,
                SUM(id.Defect_Count) as Total_Count
            FROM image_defects id
            INNER JOIN image_uploaded iu ON id.Image_ID = iu.Image_ID
            WHERE iu.User_ID = ?
        ";
    }
    $defectParams = [$user];

    if ($project) {
        $defectTypesQuery .= " AND iu.Project_Name = ?";
        $defectParams[] = $project;
    }

    if ($date) {
        $defectTypesQuery .= " AND DATE(iu.Upload_Date) = ?";
        $defectParams[] = $date;
    }

    if ($image_id) {
        $defectTypesQuery .= " AND iu.Image_ID = ?";
        $defectParams[] = $image_id;
    }

    $defectTypesQuery .= " GROUP BY id.Defect_Type, id.Maintenance_Type ORDER BY Total_Count DESC";

    $defectStmt = $mysqli->prepare($defectTypesQuery);
    $defectStmt->bind_param(str_repeat("s", count($defectParams)), ...$defectParams);
    $defectStmt->execute();
    $defectResult = $defectStmt->get_result();

    $defectTypes = [];
    $maintenanceTypes = [];
    $totalDefects = 0;

    while ($row = $defectResult->fetch_assoc()) {
        // Calculate maintenance type if not in database
        if (!$hasMaintenanceColumn || empty($row['Maintenance_Type'])) {
            $row['Maintenance_Type'] = calculateMaintenanceType($row['Defect_Type']);
        }

        $defectTypes[] = $row;
        $totalDefects += $row['Total_Count'];

        // Count maintenance types
        $maintenanceType = $row['Maintenance_Type'];
        if (!isset($maintenanceTypes[$maintenanceType])) {
            $maintenanceTypes[$maintenanceType] = 0;
        }
        $maintenanceTypes[$maintenanceType] += $row['Total_Count'];
    }

    function calculateMaintenanceType($defectType) {
        $defectLower = strtolower(str_replace('_', '-', $defectType));

        if (strpos($defectLower, 'clean') !== false) {
            return 'None';
        } elseif (strpos($defectLower, 'bird-drop') !== false || strpos($defectLower, 'dusty') !== false) {
            return 'Clean';
        } elseif (strpos($defectLower, 'electrical-damage') !== false) {
            return 'Repair';
        } elseif (strpos($defectLower, 'physical-damage') !== false) {
            return 'Seal';
        } else {
            return 'Inspect';
        }
    }

    // Calculate percentages for defect types
    foreach ($defectTypes as &$defect) {
        $defect['percentage'] = $totalDefects > 0 ? round(($defect['Total_Count'] / $totalDefects) * 100, 1) : 0;
    }

    // Find most common maintenance type
    $commonMaintenanceType = "-";
    if (!empty($maintenanceTypes)) {
        arsort($maintenanceTypes);
        $commonMaintenanceType = array_key_first($maintenanceTypes);
    }

    $response = [
        'defect_types' => $defectTypes,
        'maintenance_types' => $maintenanceTypes,
        'common_maintenance_type' => $commonMaintenanceType,
        'total_defects' => $totalDefects
    ];

    echo json_encode($response);
} else {
    http_response_code(400);
    echo json_encode(["error" => "Invalid API type"]);
}

$mysqli->close();
?>
