/* === GLOBAL STYLES === */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: #f4faff;
  color: #333;
  line-height: 1.6;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* === MAIN CONTAINER === */
.container {
  flex: 1;
  padding: 2.5cm 40px 1cm 40px; /* More space below header, 1cm above footer */
  display: flex;
  justify-content: center;
  align-items: flex-start;
  background: #f4faff;
  min-height: calc(100vh - 80px);
}

/* === TABLE SECTION === */
.table-container {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.08);
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e9ecef;
}

.table-header h2 {
  color: #002b5b;
  font-size: 1.8rem;
  font-weight: 600;
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.sort-controls label {
  font-weight: 500;
  color: #666;
}

.sort-controls select {
  padding: 8px 12px;
  border: 2px solid #ddd;
  border-radius: 6px;
  background: white;
  color: #333;
  font-size: 14px;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.sort-controls select:focus {
  outline: none;
  border-color: #4da8da;
}

/* === TABLE STYLES === */
.table-wrapper {
  flex: 1;
  overflow-x: auto;
  overflow-y: auto;
  max-height: 60vh;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  min-width: 1200px;
}

thead {
  background: linear-gradient(135deg, #002b5b 0%, #1e4a72 100%);
  color: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

thead th {
  padding: 15px 12px;
  text-align: left;
  font-weight: 600;
  font-size: 12px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  border-right: 1px solid rgba(255,255,255,0.1);
}

thead th:last-child {
  border-right: none;
}

tbody tr {
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s ease;
}

tbody tr:hover {
  background-color: #f8f9fa;
}

tbody tr:nth-child(even) {
  background-color: #fafbfc;
}

tbody tr:nth-child(even):hover {
  background-color: #f1f3f4;
}

tbody td {
  padding: 12px;
  vertical-align: top;
  border-right: 1px solid #e9ecef;
}

tbody td:last-child {
  border-right: none;
}

/* === LINK STYLES === */
.view-link {
  color: #4da8da;
  text-decoration: none;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  display: inline-block;
}

.view-link:hover {
  background-color: #4da8da;
  color: white;
  text-decoration: none;
}

/* === ACTION LINK STYLES === */
.action-link {
  color: white;
  text-decoration: none;
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  display: inline-block;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.dashboard-link {
  background: linear-gradient(135deg, #28a745, #20c997);
}

.dashboard-link:hover {
  background: linear-gradient(135deg, #20c997, #28a745);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
  text-decoration: none;
  color: white;
}

.overview-link {
  background: linear-gradient(135deg, #007bff, #0056b3);
}

.overview-link:hover {
  background: linear-gradient(135deg, #0056b3, #007bff);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
  text-decoration: none;
  color: white;
}

/* === STATUS BADGES === */
.status-analyzed {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-pending {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.analysis-date {
  font-size: 11px;
  color: #666;
  margin-top: 4px;
}

/* === DEFECT RATE STYLES === */
.defect-rate {
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 12px;
}

.defect-rate.low {
  background: #d4edda;
  color: #155724;
}

.defect-rate.medium {
  background: #fff3cd;
  color: #856404;
}

.defect-rate.high {
  background: #f8d7da;
  color: #721c24;
}

/* === NO DATA SECTION === */
.no-data {
  text-align: center;
  padding: 3rem 2rem;
  color: #666;
}

.no-data-icon {
  width: 120px;
  height: 120px;
  margin-bottom: 1.5rem;
  opacity: 0.7;
}

.no-data h3 {
  color: #002b5b;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.no-data p {
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.upload-btn {
  background: linear-gradient(135deg, #4da8da, #002b5b);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-block;
}

.upload-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(77, 168, 218, 0.3);
  text-decoration: none;
  color: white;
}

/* === PAGINATION === */
.pagination-container {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

.pagination-controls {
  display: flex;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

.pagination-btn {
  padding: 8px 12px;
  background: white;
  border: 2px solid #ddd;
  color: #666;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 40px;
  text-align: center;
}

.pagination-btn:hover {
  background: #f8f9fa;
  border-color: #4da8da;
  color: #4da8da;
  text-decoration: none;
}

.pagination-btn.active {
  background: linear-gradient(135deg, #4da8da, #002b5b);
  border-color: #4da8da;
  color: white;
}

.pagination-btn.active:hover {
  background: linear-gradient(135deg, #002b5b, #4da8da);
}

/* === RESPONSIVE DESIGN === */
:root {
  --header-h-mobile: 64px; /* your header height */
}

@media (max-width: 768px) {
  html, body {
    height: auto !important;
    min-height: 100vh;
    overflow-x: hidden;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch;
  }

  /* Push content down below fixed header + 0.5cm gap */
  .container {
    margin-top: calc(var(--header-h-mobile) + 0.5cm) !important;
    margin-bottom: 0.5cm !important; /* gap above footer */
    padding: 0 10px; /* side padding only */
  }

  .table-container {
    padding: 0.75rem;
    max-height: none;
  }

  .table-header {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .table-header h2 {
    font-size: 1.25rem;
  }

  .table-wrapper {
    overflow-x: auto;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    max-width: 100%;
    max-height: 60vh;
    border-radius: 8px;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    table-layout: auto;
    font-size: 12px;
    min-width: 640px;
  }

  thead th,
  tbody td {
    padding: 8px 6px;
    white-space: nowrap;
  }

  .pagination-controls {
    gap: 6px;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 10px;
  }

  .pagination-btn {
    padding: 8px 10px;
    font-size: 12px;
    min-width: 36px;
  }
}

/* Tiny phones */
@media (max-width: 480px) {
  .container {
    margin-top: calc(var(--header-h-mobile) + 0.5cm) !important;
    margin-bottom: 0.5cm !important;
    padding: 0 8px;
  }

  table {
    min-width: 560px;
    font-size: 11px;
  }

  thead th,
  tbody td {
    padding: 8px 5px;
  }
}
