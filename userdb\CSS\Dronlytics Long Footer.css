/* === FOOTER STYLES === */
footer {
  background: #002f5f;
  color: white;
  padding: 4rem 0 2rem;
  width: 100%;
  position: relative;
  z-index: 10;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 3rem;
}

/* === FOOTER LEFT SECTION === */
.footer-left h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #4a90e2;
}

.footer-left p {
  margin-bottom: 0.5rem;
  opacity: 0.8;
  line-height: 1.6;
}

/* === FOOTER MIDDLE & RIGHT SECTIONS === */
.footer-middle h3, 
.footer-right h3 {
  margin-bottom: 1.5rem;
  color: #4a90e2;
  font-size: 1.2rem;
}

.footer-middle p {
  opacity: 0.8;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.footer-middle a {
  color: #4a90e2;
  text-decoration: none;
}

/* === EMAIL SUBSCRIPTION FORM === */
.email-subscribe {
  display: flex;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #4a90e2;
}

.email-subscribe input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  background: white;
  color: #333;
}

.email-subscribe input::placeholder {
  color: #999;
}

.email-subscribe button {
  background: #4a90e2;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  transition: background 0.3s ease;
  font-weight: 600;
}

.email-subscribe button:hover {
  background: #357abd;
}

/* === SOCIAL MEDIA ICONS === */
.social-icons {
  display: flex;
  gap: 1rem;
}

.social-icons a {
  width: 40px;
  height: 40px;
  background: #4a90e2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-icons a:hover {
  background: white;
  color: #4a90e2;
  transform: translateY(-2px);
}

/* === RESPONSIVE DESIGN - TABLET & MOBILE === */
/* Tablet Styles */
@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem 2rem;
  }

  .nav-links {
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
  }

  .content-area {
    margin-top: 140px; /* Increased for mobile header height */
    min-height: calc(100vh - 140px - 350px);
  }

  .content-iframe {
    min-height: calc(100vh - 490px);
  }
  
  .footer-container {
    grid-template-columns: 1fr;
    text-align: center;
  }

  footer {
    padding: 3rem 0 2rem;
  }
}

/* Mobile Styles */
@media (max-width: 480px) {
  .header-container {
    padding: 1rem;
  }

  .content-area {
    margin-top: 160px;
  }

  .content-iframe {
    min-height: calc(100vh - 510px);
  }
}

/* Mobile Styles */
@media (max-width: 480px) {
  .header-container {
    padding: 1rem;
  }

  .content-area {
    margin-top: 160px;
  }

  .content-iframe {
    min-height: calc(100vh - 510px);
  }

  /* Center social media icons under FOLLOW US on mobile */
  .footer-right .social-icons {
    justify-content: center;   /* centers horizontally */
    margin-top: 0.5rem;        /* small space below FOLLOW US text */
  }

  .footer-right h3 {
    text-align: center;        /* center FOLLOW US title */
  }
}
