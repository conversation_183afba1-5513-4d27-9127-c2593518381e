<?php
header("Content-Type: application/json");

$conn = new mysqli("localhost", "root", "", "userdb");

if ($conn->connect_error) {
    echo json_encode(["error" => "Database connection failed"]);
    exit();
}

$result = $conn->query("SELECT Image_ID FROM image_uploaded ORDER BY Image_ID DESC LIMIT 1");

if ($row = $result->fetch_assoc()) {
    $lastId = $row['Image_ID'];  // e.g. IMG014
    $lastNum = intval(substr($lastId, 3));
} else {
    $lastNum = 0;
}

echo json_encode(["next_id" => $lastNum + 1]);
?>
