#!/usr/bin/env python3
"""
Manual script to process images and generate annotated versions
This script will:
1. Find images without annotated paths
2. Download them from Google Drive
3. Run ML model to create annotated images
4. Update database with annotated image paths
"""

import mysql.connector
import requests
import os
import cv2
from ultralytics import YOLO
import numpy as np

# Database configuration
DB_CONFIG = {
    'host': 'localhost', 'user': 'root', 'password': '',
    'database': 'userdb', 'charset': 'utf8mb4', 'autocommit': True
}

def get_db_connection():
    try:
        return mysql.connector.connect(**DB_CONFIG)
    except Exception as e:
        print("❌ DB Error:", e)
        return None

def load_model():
    """Load YOLO model"""
    try:
        model = YOLO("../model/best1.pt")
        print("✅ Model loaded successfully")
        return model
    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        return None

def download_image_from_drive(drive_url, local_path):
    """Download image from Google Drive URL"""
    try:
        # Extract file ID from Google Drive URL
        if "id=" in drive_url:
            file_id = drive_url.split("id=")[1].split("&")[0]
        elif "/d/" in drive_url:
            file_id = drive_url.split("/d/")[1].split("/")[0]
        else:
            print(f"❌ Cannot extract file ID from URL: {drive_url}")
            return False
        
        # Try different download URLs
        download_urls = [
            f"https://drive.google.com/uc?export=download&id={file_id}",
            f"https://lh3.googleusercontent.com/d/{file_id}",
        ]
        
        for url in download_urls:
            try:
                response = requests.get(url, timeout=30)
                if response.status_code == 200 and len(response.content) > 1000:  # Valid image
                    with open(local_path, 'wb') as f:
                        f.write(response.content)
                    print(f"✅ Downloaded image: {local_path}")
                    return True
            except Exception as e:
                print(f"⚠️ Failed URL {url}: {e}")
                continue
        
        print(f"❌ Failed to download from all URLs for {drive_url}")
        return False
        
    except Exception as e:
        print(f"❌ Download error: {e}")
        return False

def get_maintenance_type(defect_type, bbox_area=None):
    """Get maintenance type based on defect type"""
    defect_lower = defect_type.lower().replace('_', '-').replace(' ', '-')

    if 'clean' in defect_lower:
        return 'none'
    elif 'bird-drop' in defect_lower or 'dusty' in defect_lower:
        return 'clean'
    elif 'electrical-damage' in defect_lower:
        return 'repair'
    elif 'crack' in defect_lower:
        return 'replace'  # Crack defects always require replacement
    elif 'physical-damage' in defect_lower:
        return 'replace'  # Physical damage always requires replacement
    else:
        return 'inspect'

def save_defect_to_db(image_id, defect_type, defect_count):
    """Save defect information to image_defects table"""
    conn = get_db_connection()
    if not conn:
        return False

    try:
        maintenance_type = get_maintenance_type(defect_type)
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO image_defects (Image_ID, Defect_Type, Defect_Count, Maintenance_Type)
            VALUES (%s, %s, %s, %s)
        """, (image_id, defect_type, defect_count, maintenance_type))
        conn.commit()
        print(f"✅ Saved defect: {image_id} -> {defect_type} x{defect_count} ({maintenance_type})")
        return True
    except Exception as e:
        print(f"❌ Database defect save error: {e}")
        return False
    finally:
        conn.close()

def create_annotated_image(image_path, model, image_id):
    """Create annotated image with defect detection and save defects to DB"""
    try:
        # Run inference
        results = model(image_path)[0]

        # Read original image
        img = cv2.imread(image_path)
        if img is None:
            print(f"❌ Failed to read image: {image_path}")
            return None

        # Create annotated version
        annotated_img = img.copy()

        # Count defects by type
        defect_counts = {}

        # Draw detections
        if results.boxes is not None:
            for i, box in enumerate(results.boxes):
                x1, y1, x2, y2 = map(int, box.xyxy[0])
                confidence = float(box.conf[0])
                cls = int(box.cls[0])
                class_name = model.names[cls]

                # Count defects
                defect_counts[class_name] = defect_counts.get(class_name, 0) + 1

                # Color mapping for defect types
                colors = {
                    'Physical-Damage': (0, 0, 255),    # Red
                    'Bird-drop': (0, 255, 255),        # Yellow
                    'Dusty': (0, 165, 255),            # Orange
                    'Electrical-damage': (255, 0, 255), # Magenta
                }
                color = colors.get(class_name, (0, 255, 0))  # Default green

                # Draw bounding box
                cv2.rectangle(annotated_img, (x1, y1), (x2, y2), color, 2)

                # Draw segmentation mask if available
                if results.masks is not None and i < len(results.masks):
                    mask = results.masks[i].data[0].cpu().numpy()
                    mask_resized = cv2.resize(mask, (annotated_img.shape[1], annotated_img.shape[0]))
                    mask_indices = mask_resized > 0.5
                    overlay = annotated_img.copy()
                    overlay[mask_indices] = color
                    alpha = 0.3
                    annotated_img = cv2.addWeighted(annotated_img, 1-alpha, overlay, alpha, 0)

                # Add label
                label = f"{class_name}: {confidence:.2f}"
                cv2.putText(annotated_img, label, (x1, y1 - 5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)

        # Save defect counts to database
        for defect_type, count in defect_counts.items():
            save_defect_to_db(image_id, defect_type, count)

        # Save annotated image
        annotated_filename = f"{image_id}_annotated.jpg"
        annotated_path = os.path.join("static/annotated", annotated_filename)
        # Use forward slashes for web paths
        annotated_web_path = f"static/annotated/{annotated_filename}"

        # Ensure directory exists
        os.makedirs("static/annotated", exist_ok=True)

        # Save with compression
        encode_params = [cv2.IMWRITE_JPEG_QUALITY, 85]
        success = cv2.imwrite(annotated_path, annotated_img, encode_params)

        if success:
            print(f"✅ Created annotated image: {annotated_path}")
            return annotated_web_path
        else:
            print(f"❌ Failed to save annotated image: {annotated_path}")
            return None

    except Exception as e:
        print(f"❌ Error creating annotated image: {e}")
        return None

def update_annotated_path(image_id, annotated_path):
    """Update database with annotated image path"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        cursor.execute(
            "UPDATE image_uploaded SET Annotated_Image_Path = %s WHERE Image_ID = %s",
            (annotated_path, image_id)
        )
        conn.commit()
        print(f"✅ Updated database: {image_id} -> {annotated_path}")
        return True
    except Exception as e:
        print(f"❌ Database update error: {e}")
        return False
    finally:
        conn.close()

def main():
    """Main processing function"""
    print("🚀 Starting manual image processing...")
    
    # Load model
    model = load_model()
    if not model:
        print("❌ Cannot proceed without model")
        return
    
    # Get images without annotated paths
    conn = get_db_connection()
    if not conn:
        print("❌ Cannot connect to database")
        return
    
    try:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT Image_ID, Image_Path
            FROM image_uploaded
            WHERE User_ID IS NOT NULL
            LIMIT 10
        """)
        images_to_process = cursor.fetchall()
        
        print(f"📋 Found {len(images_to_process)} images to process")
        
        for image_id, image_path in images_to_process:
            print(f"\n🔄 Processing {image_id}...")

            # Clear existing defects for this image
            clear_conn = get_db_connection()
            if clear_conn:
                try:
                    clear_cursor = clear_conn.cursor()
                    clear_cursor.execute("DELETE FROM image_defects WHERE Image_ID = %s", (image_id,))
                    clear_conn.commit()
                    print(f"🗑️ Cleared existing defects for {image_id}")
                except Exception as e:
                    print(f"⚠️ Failed to clear existing defects: {e}")
                finally:
                    clear_conn.close()

            # Download image
            local_path = f"temp_{image_id}.jpg"
            if download_image_from_drive(image_path, local_path):
                # Create annotated version and save defects
                annotated_path = create_annotated_image(local_path, model, image_id)
                if annotated_path:
                    # Update database with annotated path
                    update_annotated_path(image_id, annotated_path)

                # Clean up
                try:
                    os.remove(local_path)
                except:
                    pass
            else:
                print(f"⚠️ Skipping {image_id} due to download failure")
        
        print(f"\n✅ Processing complete!")
        
    except Exception as e:
        print(f"❌ Main process error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    main()
